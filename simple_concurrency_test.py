#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版并发性能测试脚本
测试不同并发配置下的翻译性能，找到最适合的并发数
"""

import json
import time
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Tuple
import logging
import statistics

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleConcurrencyTester:
    """简化版并发性能测试器"""

    def __init__(self, model_name: str = "/home/<USER>/Model/LLM/Qwen/Qwen3-8B-AWQ",
                 base_url: str = "http://localhost:8666"):
        self.model_name = model_name
        self.base_url = base_url
        self.api_url = f"{base_url}/v1/chat/completions"
        self.session = requests.Session()

        # 配置session
        self.session.headers.update({
            'Connection': 'keep-alive',
            'Content-Type': 'application/json'
        })

    def check_vllm_connection(self) -> bool:
        """检查VLLM服务是否可用"""
        try:
            test_payload = {
                "model": self.model_name,
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10,
                "temperature": 0.1
            }

            response = self.session.post(self.api_url, json=test_payload, timeout=30)
            response.raise_for_status()
            logger.info("✅ VLLM服务连接正常")
            return True
        except Exception as e:
            logger.error(f"❌ 无法连接到VLLM服务: {e}")
            return False

    def create_test_data(self, count: int = 10) -> List[Dict]:
        """创建测试数据"""
        test_items = []
        for i in range(count):
            item = {
                "row_number": i + 1,
                "problem": f"Solve the equation x^2 + {i+1}x + {i} = 0",
                "generated_solution": f"Using the quadratic formula for x^2 + {i+1}x + {i} = 0, we get x = (-{i+1} ± √({i+1}² - 4·1·{i})) / (2·1)",
                "expected_answer": f"x = {-i-1}/2 ± √({(i+1)**2 - 4*i})/2"
            }
            test_items.append(item)
        return test_items

    def translate_single_item(self, item: Dict) -> Tuple[bool, float]:
        """翻译单个条目，返回(成功状态, 耗时)"""
        start_time = time.time()

        prompt = f"""请将以下JSON对象翻译成中文并返回完整JSON：

{json.dumps(item, ensure_ascii=False, indent=2)}

翻译规则：
1. 保持数学公式不变
2. 直接覆盖原字段内容
3. 返回有效JSON格式
4. 不要添加解释

请直接返回翻译后的JSON："""

        payload = {
            "model": self.model_name,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.1,
            "max_tokens": 10240,
            "stream": False,
            "extra_body": {
                "chat_template_kwargs": {
                    "enable_thinking": False
                }
            }
        }

        try:
            response = self.session.post(self.api_url, json=payload, timeout=60)
            response.raise_for_status()

            result = response.json()
            translated_text = result['choices'][0]['message']['content'].strip()

            # 简单验证是否包含中文
            if any('\u4e00' <= char <= '\u9fff' for char in translated_text):
                duration = time.time() - start_time
                return True, duration

        except Exception as e:
            logger.warning(f"翻译失败: {e}")

        duration = time.time() - start_time
        return False, duration

    def test_concurrency_level(self, test_data: List[Dict], max_workers: int) -> Dict:
        """测试特定并发级别的性能"""
        print(f"🧪 测试并发数: {max_workers}")

        start_time = time.time()
        success_count = 0
        failed_count = 0
        durations = []

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            futures = [executor.submit(self.translate_single_item, item) for item in test_data]

            # 收集结果
            for future in as_completed(futures):
                success, duration = future.result()
                durations.append(duration)

                if success:
                    success_count += 1
                else:
                    failed_count += 1

        total_time = time.time() - start_time

        # 计算统计信息
        avg_duration = statistics.mean(durations) if durations else 0
        throughput = len(test_data) / total_time if total_time > 0 else 0
        success_rate = success_count / len(test_data) * 100 if test_data else 0

        return {
            'max_workers': max_workers,
            'total_time': total_time,
            'success_count': success_count,
            'failed_count': failed_count,
            'success_rate': success_rate,
            'avg_duration': avg_duration,
            'throughput': throughput
        }

    def run_test(self, test_count: int = 10) -> List[Dict]:
        """运行并发测试"""
        if not self.check_vllm_connection():
            return []

        # 创建测试数据
        test_data = self.create_test_data(test_count)
        print(f"📊 创建了 {len(test_data)} 个测试条目")

        # 测试不同的并发级别 (从5开始)
        concurrency_levels = [5, 8, 10, 12, 15]
        results = []

        print(f"\n🚀 开始并发性能测试")
        print("=" * 80)
        print(f"{'并发数':<6} {'总时间':<8} {'吞吐量':<10} {'成功率':<8} {'平均延迟':<10}")
        print("-" * 80)

        for max_workers in concurrency_levels:
            try:
                result = self.test_concurrency_level(test_data, max_workers)
                results.append(result)

                # 显示结果
                print(f"{result['max_workers']:<6} "
                      f"{result['total_time']:<8.1f} "
                      f"{result['throughput']:<10.2f} "
                      f"{result['success_rate']:<8.1f} "
                      f"{result['avg_duration']:<10.2f}")

                # 测试间隔
                time.sleep(1)

            except Exception as e:
                logger.error(f"测试并发数 {max_workers} 时出错: {e}")
                continue

        return results

    def analyze_results(self, results: List[Dict]):
        """分析测试结果"""
        if not results:
            return

        print("\n" + "=" * 60)
        print("📈 性能分析结果")
        print("=" * 60)

        # 找到最佳配置
        best_throughput = max(results, key=lambda x: x['throughput'])
        best_success_rate = max(results, key=lambda x: x['success_rate'])

        print(f"🏆 最高吞吐量: 并发数 {best_throughput['max_workers']}, "
              f"{best_throughput['throughput']:.2f} 条/s")
        print(f"✅ 最高成功率: 并发数 {best_success_rate['max_workers']}, "
              f"{best_success_rate['success_rate']:.1f}%")

        # 综合评分
        for result in results:
            if result['avg_duration'] > 0:
                result['score'] = (result['throughput'] * result['success_rate']) / result['avg_duration']
            else:
                result['score'] = 0

        best_overall = max(results, key=lambda x: x['score'])

        print(f"\n🎯 推荐配置: 并发数 {best_overall['max_workers']}")
        print(f"   - 吞吐量: {best_overall['throughput']:.2f} 条/s")
        print(f"   - 成功率: {best_overall['success_rate']:.1f}%")
        print(f"   - 平均延迟: {best_overall['avg_duration']:.2f}s")

        print(f"\n💡 建议在 json_FY.py 中设置:")
        print(f"   max_concurrent = {best_overall['max_workers']}")
        print(f"   max_file_workers = {min(5, best_overall['max_workers'])}")

def main():
    """主函数"""
    print("🧪 VLLM并发性能测试工具 (简化版)")
    print("=" * 60)

    # 获取用户输入
    try:
        test_count = int(input("请输入测试条目数量 (建议5-10): ") or "8")
    except ValueError:
        test_count = 8

    # 创建测试器
    tester = SimpleConcurrencyTester()

    # 运行测试
    results = tester.run_test(test_count)

    if results:
        # 分析结果
        tester.analyze_results(results)

        # 保存结果
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        result_file = f"simple_concurrency_results_{timestamp}.json"

        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"\n💾 测试结果已保存到: {result_file}")
    else:
        print("❌ 测试失败，请检查VLLM服务状态")

if __name__ == "__main__":
    main()
