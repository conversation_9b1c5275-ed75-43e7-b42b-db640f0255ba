#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
读取parquet文件并使用VLLM的Qwen3-8B-AWQ模型进行高效并发中文翻译
支持断点续存、进度显示、自动备份等功能
"""

import pandas as pd
import requests
import time
import os
from typing import List
import logging
import re
import shutil
from tqdm import tqdm
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 配置tqdm进度条样式
def setup_progress_bars():
    """配置进度条的全局样式"""
    # 设置tqdm的默认参数
    tqdm.pandas()
    # 禁用tqdm的警告
    import warnings
    warnings.filterwarnings("ignore", category=UserWarning, module="tqdm")

class VLLMTranslator:
    """使用VLLM API进行翻译的类，支持并发翻译"""

    def __init__(self, model_name: str = "/home/<USER>/Model/LLM/Qwen/Qwen3-8B-AWQ", base_url: str = "http://localhost:8666", max_concurrent: int = 10):
        self.model_name = model_name
        self.base_url = base_url
        self.api_url = f"{base_url}/v1/chat/completions"
        self.max_concurrent = max_concurrent
        self.session = requests.Session()  # 复用连接
        self.rate_limiter = threading.Semaphore(max_concurrent)  # 限制并发数

        # 超时配置
        self.connect_timeout = 30  # 连接超时
        self.read_timeout = 300    # 读取超时（5分钟）
        self.total_timeout = 360   # 总超时（6分钟）

        # 配置session
        self.session.headers.update({
            'Connection': 'keep-alive',
            'Content-Type': 'application/json'
        })

        # 设置连接池参数
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=max_concurrent,
            pool_maxsize=max_concurrent * 2,
            max_retries=0  # 我们自己处理重试
        )
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)

    def translate_text(self, text: str, max_retries: int = 3) -> str:
        """
        使用VLLM翻译文本到中文（线程安全版本）

        Args:
            text: 要翻译的文本
            max_retries: 最大重试次数

        Returns:
            翻译后的中文文本
        """
        if not text or pd.isna(text):
            return ""

        with self.rate_limiter:  # 限制并发数
            return self._translate_single(text, max_retries)

    def _translate_single(self, text: str, max_retries: int = 3) -> str:
        """内部翻译方法"""
        # 构建翻译提示
        prompt = f"""请将以下英文内容翻译成中文，保持数学公式不变。

英文原文：
{text}

请直接输出中文翻译，不要包含任何解释、说明或思考过程："""

        payload = {
            "model": self.model_name,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 4096,
            "stream": False,
            "extra_body": {
                "chat_template_kwargs": {
                    "enable_thinking": False
                }
            }
        }

        for attempt in range(max_retries):
            try:
                # 使用分离的超时设置
                timeout = (self.connect_timeout, self.read_timeout)

                response = self.session.post(
                    self.api_url,
                    json=payload,
                    timeout=timeout
                )
                response.raise_for_status()

                result = response.json()
                translated_text = result['choices'][0]['message']['content'].strip()

                # 清理思考标签和前缀
                # 移除 <think>...</think> 标签及其内容
                translated_text = re.sub(r'<think>.*?</think>', '', translated_text, flags=re.DOTALL).strip()

                # 清理可能的前缀
                if translated_text.startswith("中文翻译："):
                    translated_text = translated_text[5:].strip()
                elif translated_text.startswith("翻译："):
                    translated_text = translated_text[3:].strip()

                if translated_text:
                    return translated_text
                else:
                    logger.warning("翻译结果为空")

            except requests.exceptions.Timeout as e:
                logger.error(f"超时错误 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    wait_time = min(2 ** attempt, 30)  # 最大等待30秒
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)

            except requests.exceptions.ConnectionError as e:
                logger.error(f"连接错误 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    wait_time = min(2 ** attempt, 30)
                    logger.info(f"连接失败，等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)

            except requests.exceptions.RequestException as e:
                logger.error(f"请求错误 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    wait_time = min(2 ** attempt, 30)
                    time.sleep(wait_time)

            except Exception as e:
                logger.error(f"翻译错误 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    wait_time = min(2 ** attempt, 30)
                    time.sleep(wait_time)

        logger.error("翻译失败，返回原文")
        return text  # 如果翻译失败，返回原文

    def check_vllm_connection(self) -> bool:
        """检查VLLM服务是否可用"""
        try:
            # 测试简单的请求
            test_payload = {
                "model": self.model_name,
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10,
                "temperature": 0.1
            }

            timeout = (self.connect_timeout, min(self.read_timeout, 30))  # 测试时使用较短的超时
            response = self.session.post(
                self.api_url,
                json=test_payload,
                timeout=timeout
            )
            response.raise_for_status()

            logger.info(f"VLLM服务正常，模型 {self.model_name} 可用")
            return True

        except Exception as e:
            logger.error(f"无法连接到VLLM服务: {e}")
            return False

    def diagnose_vllm_service(self) -> dict:
        """诊断VLLM服务状态"""
        diagnosis = {
            "service_reachable": False,
            "model_loaded": False,
            "response_time": None,
            "error_details": None
        }

        try:
            import time
            start_time = time.time()

            # 检查服务是否可达
            health_url = f"{self.base_url}/health"
            try:
                health_response = requests.get(health_url, timeout=10)
                if health_response.status_code == 200:
                    diagnosis["service_reachable"] = True
                    logger.info("✅ VLLM服务可达")
                else:
                    logger.warning(f"⚠️ VLLM健康检查返回状态码: {health_response.status_code}")
            except Exception as e:
                logger.error(f"❌ VLLM服务不可达: {e}")
                diagnosis["error_details"] = str(e)
                return diagnosis

            # 检查模型是否加载
            models_url = f"{self.base_url}/v1/models"
            try:
                models_response = requests.get(models_url, timeout=10)
                if models_response.status_code == 200:
                    models_data = models_response.json()
                    available_models = [model["id"] for model in models_data.get("data", [])]
                    if self.model_name in available_models:
                        diagnosis["model_loaded"] = True
                        logger.info(f"✅ 模型 {self.model_name} 已加载")
                    else:
                        logger.error(f"❌ 模型 {self.model_name} 未找到")
                        logger.info(f"可用模型: {available_models}")
            except Exception as e:
                logger.warning(f"⚠️ 无法获取模型列表: {e}")

            # 测试推理响应时间
            if diagnosis["service_reachable"]:
                test_result = self.check_vllm_connection()
                end_time = time.time()
                diagnosis["response_time"] = end_time - start_time

                if test_result:
                    logger.info(f"✅ 推理测试成功，响应时间: {diagnosis['response_time']:.2f}秒")
                else:
                    logger.error("❌ 推理测试失败")

        except Exception as e:
            diagnosis["error_details"] = str(e)
            logger.error(f"诊断过程出错: {e}")

        return diagnosis

    def translate_batch(self, texts: List[str], max_workers: int = None, progress_bar=None) -> List[str]:
        """
        批量并发翻译文本

        Args:
            texts: 要翻译的文本列表
            max_workers: 最大工作线程数，默认为max_concurrent
            progress_bar: 外部传入的进度条对象

        Returns:
            翻译后的文本列表
        """
        if not texts:
            return []

        if max_workers is None:
            max_workers = self.max_concurrent

        results = [""] * len(texts)  # 预分配结果列表
        completed_count = 0
        failed_count = 0

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有翻译任务
            future_to_index = {
                executor.submit(self.translate_text, text): i
                for i, text in enumerate(texts) if text and pd.notna(text) and str(text).strip()
            }

            # 收集结果
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    result = future.result()
                    results[index] = result
                    # 检查是否翻译成功（结果不等于原文）
                    if result == str(texts[index]):
                        failed_count += 1
                except Exception as e:
                    logger.error(f"批量翻译第{index}项失败: {e}")
                    results[index] = str(texts[index])  # 失败时返回原文
                    failed_count += 1

                completed_count += 1
                if progress_bar:
                    progress_bar.update(1)
                    success_rate = ((completed_count - failed_count) / completed_count * 100) if completed_count > 0 else 0
                    progress_bar.set_postfix({
                        "已完成": f"{completed_count}/{len(future_to_index)}",
                        "成功率": f"{success_rate:.1f}%",
                        "失败": failed_count
                    })

        return results

class ParquetTranslator:
    """处理parquet文件翻译的主类 - 分块处理版本"""

    def __init__(self, file_path: str, translator: VLLMTranslator, chunk_size: int = 1000):
        self.file_path = file_path
        self.translator = translator
        self.df = None
        self.chunk_size = chunk_size
        self.chunks_dir = "translation_chunks"
        self.progress_dir = "translation_progress"
        self.state_file = os.path.join(self.progress_dir, "translation_state.json")
        os.makedirs(self.chunks_dir, exist_ok=True)
        os.makedirs(self.progress_dir, exist_ok=True)

        # 进度跟踪变量
        self.total_tasks = 0
        self.completed_tasks = 0
        self.start_time = None

    def save_state(self, column: str, row_index: int, total_rows: int):
        """保存翻译状态"""
        state = {
            "current_column": column,
            "current_row": row_index,
            "total_rows": total_rows,
            "timestamp": time.time(),
            "completed_columns": getattr(self, 'completed_columns', [])
        }
        try:
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存状态失败: {e}")

    def load_state(self):
        """加载翻译状态"""
        if os.path.exists(self.state_file):
            try:
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载状态失败: {e}")
        return None

    def clear_state(self):
        """清除翻译状态"""
        if os.path.exists(self.state_file):
            os.remove(self.state_file)

    def load_parquet(self) -> bool:
        """加载parquet文件"""
        try:
            logger.info(f"正在加载文件: {self.file_path}")
            self.df = pd.read_parquet(self.file_path)
            logger.info(f"文件加载成功! 数据形状: {self.df.shape}")
            logger.info(f"列名: {list(self.df.columns)}")
            return True
        except Exception as e:
            logger.error(f"加载文件失败: {e}")
            return False

    def split_into_chunks(self) -> List[str]:
        """将大文件分割成小块文件"""
        if self.df is None:
            logger.error("请先加载parquet文件")
            return []

        # 确保块文件目录存在
        os.makedirs(self.chunks_dir, exist_ok=True)

        chunk_files = []
        total_rows = len(self.df)
        num_chunks = (total_rows + self.chunk_size - 1) // self.chunk_size

        logger.info(f"开始分割文件: {total_rows} 行 -> {num_chunks} 个块")

        with tqdm(total=num_chunks, desc="✂️ 分割文件", unit="块", colour='orange') as pbar:
            for i in range(num_chunks):
                start_idx = i * self.chunk_size
                end_idx = min((i + 1) * self.chunk_size, total_rows)

                # 获取数据块
                chunk_df = self.df.iloc[start_idx:end_idx].copy()

                # 生成块文件名
                chunk_filename = f"chunk_{i:04d}_{start_idx}_{end_idx-1}.parquet"
                chunk_path = os.path.join(self.chunks_dir, chunk_filename)

                # 保存块文件
                chunk_df.to_parquet(chunk_path, index=False)
                chunk_files.append(chunk_path)

                pbar.update(1)
                pbar.set_postfix({
                    "当前块": f"{start_idx}-{end_idx-1}",
                    "行数": len(chunk_df),
                    "进度": f"{i+1}/{num_chunks}"
                })

        logger.info(f"文件分割完成，生成 {len(chunk_files)} 个块文件")
        return chunk_files

    def get_existing_chunks(self) -> List[str]:
        """获取已存在的块文件"""
        if not os.path.exists(self.chunks_dir):
            return []

        chunk_files = []
        for filename in os.listdir(self.chunks_dir):
            if filename.startswith("chunk_") and filename.endswith(".parquet"):
                chunk_path = os.path.join(self.chunks_dir, filename)
                chunk_files.append(chunk_path)

        # 按文件名排序确保顺序正确
        chunk_files.sort()
        return chunk_files

    def translate_chunk_file(self, chunk_path: str, columns_to_translate: List[str], overall_progress=None) -> bool:
        """翻译单个块文件"""
        try:
            # 读取块文件
            chunk_df = pd.read_parquet(chunk_path)
            chunk_name = os.path.basename(chunk_path)

            # 检查是否已经翻译过
            translated_columns = [f"{col}_cn" for col in columns_to_translate]
            if all(col in chunk_df.columns for col in translated_columns):
                logger.info(f"块文件 {chunk_name} 已翻译，跳过")
                # 更新总体进度
                if overall_progress:
                    chunk_tasks = sum(len([text for text in chunk_df[col] if pd.notna(text) and str(text).strip()])
                                    for col in columns_to_translate if col in chunk_df.columns)
                    overall_progress.update(chunk_tasks)
                return True

            # 为每个需要翻译的列添加中文列
            for column in columns_to_translate:
                if column in chunk_df.columns:
                    new_column = f"{column}_cn"
                    if new_column not in chunk_df.columns:
                        chunk_df[new_column] = ""

            # 计算当前块的总任务数
            chunk_total_tasks = 0
            for column in columns_to_translate:
                if column in chunk_df.columns:
                    chunk_total_tasks += len([text for text in chunk_df[column] if pd.notna(text) and str(text).strip()])

            # 创建块级进度条
            chunk_desc = f"📄 {chunk_name} ({len(chunk_df)}行)"
            with tqdm(total=chunk_total_tasks, desc=chunk_desc, unit="条",
                     position=1, leave=False, colour='blue') as chunk_pbar:

                # 翻译每一列
                for col_idx, column in enumerate(columns_to_translate):
                    if column not in chunk_df.columns:
                        continue

                    new_column = f"{column}_cn"

                    # 准备翻译文本
                    texts_to_translate = []
                    valid_indices = []

                    for idx, text in enumerate(chunk_df[column]):
                        if pd.notna(text) and str(text).strip():
                            texts_to_translate.append(str(text))
                            valid_indices.append(idx)

                    if texts_to_translate:
                        # 更新块级进度条描述
                        chunk_pbar.set_description(f"📄 {chunk_name} - 🔤 {column} ({len(texts_to_translate)}条)")

                        # 批量翻译，传入块级进度条
                        translated_results = self.translator.translate_batch(
                            texts_to_translate,
                            progress_bar=chunk_pbar
                        )

                        # 写回结果
                        for i, idx in enumerate(valid_indices):
                            if i < len(translated_results):
                                chunk_df.iloc[idx, chunk_df.columns.get_loc(new_column)] = translated_results[i]

                        # 更新总体进度
                        if overall_progress:
                            overall_progress.update(len(texts_to_translate))

            # 保存翻译后的块文件
            chunk_df.to_parquet(chunk_path, index=False)
            return True

        except Exception as e:
            logger.error(f"翻译块文件 {chunk_path} 失败: {e}")
            return False

    def merge_chunks(self, chunk_files: List[str], output_path: str) -> bool:
        """合并所有块文件"""
        try:
            logger.info(f"开始合并 {len(chunk_files)} 个块文件")

            merged_dfs = []

            with tqdm(total=len(chunk_files), desc="🔗 读取块文件", unit="块", colour='cyan') as pbar:
                for i, chunk_path in enumerate(chunk_files):
                    chunk_name = os.path.basename(chunk_path)
                    pbar.set_description(f"🔗 读取块文件: {chunk_name}")

                    chunk_df = pd.read_parquet(chunk_path)
                    merged_dfs.append(chunk_df)
                    pbar.update(1)
                    pbar.set_postfix({
                        "当前块": chunk_name,
                        "行数": len(chunk_df),
                        "内存": f"{len(merged_dfs)}块"
                    })

            # 合并所有DataFrame
            logger.info("🔄 正在合并数据...")
            with tqdm(total=1, desc="🔄 合并数据", unit="步骤", colour='magenta') as merge_pbar:
                final_df = pd.concat(merged_dfs, ignore_index=True)
                merge_pbar.update(1)
                merge_pbar.set_postfix({"最终行数": len(final_df)})

            # 创建备份
            if os.path.exists(output_path):
                backup_path = output_path.replace('.parquet', '_backup.parquet')
                shutil.copy2(output_path, backup_path)
                logger.info(f"原文件已备份到: {backup_path}")

            # 保存合并后的文件
            final_df.to_parquet(output_path, index=False)
            logger.info(f"合并完成，保存到: {output_path}")

            # 保存CSV格式便于查看
            csv_path = output_path.replace('.parquet', '_translated.csv')
            final_df.to_csv(csv_path, index=False, encoding='utf-8')
            logger.info(f"CSV格式已保存到: {csv_path}")

            return True

        except Exception as e:
            logger.error(f"合并文件失败: {e}")
            return False

    def cleanup_chunks(self):
        """清理临时块文件"""
        try:
            if os.path.exists(self.chunks_dir):
                shutil.rmtree(self.chunks_dir)
                logger.info("临时块文件已清理")
        except Exception as e:
            logger.error(f"清理临时文件失败: {e}")

    def translate_in_chunks(self, columns_to_translate: List[str], output_path: str) -> bool:
        """
        分块翻译主方法

        Args:
            columns_to_translate: 需要翻译的列名列表
            output_path: 输出文件路径

        Returns:
            翻译是否成功
        """
        if self.df is None:
            logger.error("请先加载parquet文件")
            return False

        # 第一步：检查是否已有块文件
        existing_chunks = self.get_existing_chunks()

        if existing_chunks:
            logger.info(f"发现 {len(existing_chunks)} 个已存在的块文件")
            user_choice = input("是否使用已存在的块文件继续翻译？(y/n): ")
            if user_choice.lower() == 'y':
                chunk_files = existing_chunks
            else:
                logger.info("重新分割文件")
                self.cleanup_chunks()
                chunk_files = self.split_into_chunks()
        else:
            # 第一步：分割文件
            chunk_files = self.split_into_chunks()

        if not chunk_files:
            logger.error("文件分割失败")
            return False

        # 第二步：计算总任务数并开始翻译
        logger.info(f"开始翻译 {len(chunk_files)} 个块文件")

        # 计算总翻译任务数
        total_translation_tasks = 0
        for chunk_path in chunk_files:
            try:
                chunk_df = pd.read_parquet(chunk_path)
                for column in columns_to_translate:
                    if column in chunk_df.columns:
                        total_translation_tasks += len([text for text in chunk_df[column]
                                                      if pd.notna(text) and str(text).strip()])
            except Exception as e:
                logger.warning(f"无法读取块文件 {chunk_path} 来计算任务数: {e}")

        self.total_tasks = total_translation_tasks
        self.start_time = time.time()

        failed_chunks = []

        # 创建总体进度条
        with tqdm(total=total_translation_tasks, desc="🌍 总体翻译进度", unit="条",
                 position=0, colour='green') as overall_pbar:

            # 创建块级进度条
            with tqdm(total=len(chunk_files), desc="📦 块文件进度", unit="块",
                     position=2, leave=True, colour='yellow') as chunk_pbar:

                for chunk_idx, chunk_path in enumerate(chunk_files):
                    chunk_name = os.path.basename(chunk_path)

                    # 更新块级进度条描述
                    chunk_pbar.set_description(f"📦 处理块 {chunk_idx+1}/{len(chunk_files)}: {chunk_name}")

                    # 翻译当前块
                    success = self.translate_chunk_file(chunk_path, columns_to_translate, overall_pbar)

                    if not success:
                        failed_chunks.append(chunk_path)

                    # 更新块级进度
                    chunk_pbar.update(1)

                    # 计算并显示统计信息
                    elapsed_time = time.time() - self.start_time
                    completed_tasks = overall_pbar.n

                    if completed_tasks > 0:
                        speed = completed_tasks / elapsed_time
                        remaining_tasks = total_translation_tasks - completed_tasks
                        eta_seconds = remaining_tasks / speed if speed > 0 else 0
                        eta_hours = eta_seconds / 3600

                        overall_pbar.set_postfix({
                            "速度": f"{speed:.1f}条/秒",
                            "剩余": f"{eta_hours:.1f}h",
                            "失败块": len(failed_chunks)
                        })

                        chunk_pbar.set_postfix({
                            "当前块": chunk_name,
                            "失败": len(failed_chunks),
                            "完成率": f"{completed_tasks/total_translation_tasks*100:.1f}%"
                        })

        # 检查是否有失败的块
        if failed_chunks:
            logger.warning(f"有 {len(failed_chunks)} 个块翻译失败:")
            for chunk in failed_chunks:
                logger.warning(f"  - {os.path.basename(chunk)}")

            user_choice = input("是否重试失败的块？(y/n): ")
            if user_choice.lower() == 'y':
                logger.info("重试失败的块...")
                retry_failed = []

                # 计算重试任务数
                retry_tasks = 0
                for chunk_path in failed_chunks:
                    try:
                        chunk_df = pd.read_parquet(chunk_path)
                        for column in columns_to_translate:
                            if column in chunk_df.columns:
                                retry_tasks += len([text for text in chunk_df[column]
                                                  if pd.notna(text) and str(text).strip()])
                    except Exception:
                        pass

                # 重试进度条
                with tqdm(total=retry_tasks, desc="🔄 重试失败块", unit="条", colour='red') as retry_pbar:
                    for chunk_path in failed_chunks:
                        success = self.translate_chunk_file(chunk_path, columns_to_translate, retry_pbar)
                        if not success:
                            retry_failed.append(chunk_path)

                if retry_failed:
                    logger.error(f"仍有 {len(retry_failed)} 个块翻译失败，将跳过这些块")
                    failed_chunks = retry_failed
                else:
                    logger.info("所有块重试成功")
                    failed_chunks = []

        # 第三步：合并块文件
        if failed_chunks:
            logger.warning("由于有失败的块，将跳过失败的块进行合并")
            successful_chunks = [chunk for chunk in chunk_files if chunk not in failed_chunks]
        else:
            successful_chunks = chunk_files

        success = self.merge_chunks(successful_chunks, output_path)

        if success:
            logger.info("翻译任务完成！")

            # 询问是否清理临时文件
            user_choice = input("是否清理临时块文件？(y/n): ")
            if user_choice.lower() == 'y':
                self.cleanup_chunks()

        return success



def main():
    """主函数"""
    # 配置进度条
    setup_progress_bars()

    # 配置参数
    parquet_file_path = "/home/<USER>/WorkSpace/notebooks/datasets/OpenMathReasoning-mini_cn/data/cot-00000-of-00001.parquet"
    output_file_path = "/home/<USER>/WorkSpace/notebooks/datasets/OpenMathReasoning-mini_cn/data/cot-00000-of-00001.parquet"  # 覆盖原文件

    # 需要翻译的列（根据实际需要调整）
    columns_to_translate = [
        "problem",  # 问题
        "generated_solution",  # 生成的解决方案
        # "expected_answer",  # 预期答案（通常是数学表达式，可能不需要翻译）
    ]

    logger.info("开始parquet文件分块翻译任务")
    logger.info("=" * 60)

    # 配置参数
    max_concurrent = 5    # 最大并发请求数
    chunk_size = 20      # 每个块的行数（调整为更合理的值）

    print(f"分块翻译配置:")
    print(f"- 最大并发数: {max_concurrent}")
    print(f"- 块大小: {chunk_size} 行/块")
    print(f"- 处理方式: 分块 -> 翻译 -> 合并")

    # 初始化翻译器
    translator = VLLMTranslator(
        model_name="/home/<USER>/Model/LLM/Qwen/Qwen3-4B-AWQ",
        max_concurrent=max_concurrent
    )

    # 检查VLLM连接
    logger.info("🔍 正在检查VLLM服务状态...")
    diagnosis = translator.diagnose_vllm_service()

    if not translator.check_vllm_connection():
        logger.error("❌ 无法连接到VLLM服务")
        logger.error("📋 诊断信息:")
        logger.error(f"  - 服务可达: {'✅' if diagnosis['service_reachable'] else '❌'}")
        logger.error(f"  - 模型已加载: {'✅' if diagnosis['model_loaded'] else '❌'}")
        if diagnosis['response_time']:
            logger.error(f"  - 响应时间: {diagnosis['response_time']:.2f}秒")
        if diagnosis['error_details']:
            logger.error(f"  - 错误详情: {diagnosis['error_details']}")

        logger.error("\n🛠️ 请检查以下项目:")
        logger.error("1. VLLM服务是否正在运行在8666端口")
        logger.error("2. Qwen3-8B-AWQ模型是否已正确加载")
        logger.error("3. 服务地址是否正确 (http://localhost:8666)")
        logger.error("4. 服务器资源是否充足 (GPU/CPU/内存)")
        logger.error("5. 是否有其他进程占用端口")

        # 提供一些常用的检查命令
        logger.error("\n🔧 常用检查命令:")
        logger.error("  - 检查端口: netstat -tlnp | grep 8666")
        logger.error("  - 检查进程: ps aux | grep vllm")
        logger.error("  - 检查GPU: nvidia-smi")
        logger.error("  - 检查日志: journalctl -u vllm-service")

        return
    else:
        logger.info("✅ VLLM服务连接正常")
        if diagnosis['response_time']:
            logger.info(f"⏱️ 服务响应时间: {diagnosis['response_time']:.2f}秒")

    # 初始化parquet处理器
    processor = ParquetTranslator(parquet_file_path, translator, chunk_size=chunk_size)

    # 加载数据
    if not processor.load_parquet():
        return

    # 显示数据预览
    logger.info("数据预览:")
    logger.info(f"数据形状: {processor.df.shape}")
    logger.info(f"列名: {list(processor.df.columns)}")

    # 显示需要翻译的列的示例
    for col in columns_to_translate:
        if col in processor.df.columns:
            sample_text = processor.df[col].iloc[0] if len(processor.df) > 0 else "无数据"
            logger.info(f"\n列 '{col}' 示例:")
            logger.info(f"{str(sample_text)[:200]}...")

    # 计算分块信息
    total_rows = len(processor.df)
    num_chunks = (total_rows + chunk_size - 1) // chunk_size
    total_tasks = total_rows * len(columns_to_translate)

    # 计算预估时间（考虑并发效率和分块处理）
    estimated_seconds_per_task = 3 / (max_concurrent * 0.7)
    estimated_hours = (total_tasks * estimated_seconds_per_task) / 3600

    print(f"\n分块翻译任务概览:")
    print(f"- 总行数: {total_rows}")
    print(f"- 翻译列数: {len(columns_to_translate)}")
    print(f"- 总翻译任务: {total_tasks} 条")
    print(f"- 分块数量: {num_chunks} 个")
    print(f"- 每块大小: {chunk_size} 行")
    print(f"- 并发翻译: {max_concurrent} 线程")
    print(f"- 预估时间: {estimated_hours:.1f} 小时")
    print(f"- 支持断点续存: 是")
    print(f"- 自动备份: 是")
    print(f"- 内存优化: 是")

    # 询问用户确认
    user_input = input(f"\n是否开始分块翻译？(y/n): ")
    if user_input.lower() != 'y':
        logger.info("用户取消翻译任务")
        return

    # 开始分块翻译
    start_time = time.time()
    success = processor.translate_in_chunks(columns_to_translate, output_file_path)

    if success:
        # 显示统计信息
        end_time = time.time()
        duration = end_time - start_time
        logger.info(f"\n分块翻译完成!")
        logger.info(f"总耗时: {duration:.2f} 秒 ({duration/3600:.2f} 小时)")
        logger.info(f"处理行数: {total_rows}")
        logger.info(f"翻译列数: {len(columns_to_translate)}")
        logger.info(f"处理块数: {num_chunks}")
        logger.info(f"平均速度: {total_tasks/duration:.2f} 条/秒")

        # 显示翻译结果预览
        try:
            final_df = pd.read_parquet(output_file_path)
            logger.info("\n翻译结果预览:")
            for col in columns_to_translate:
                if col in final_df.columns and f"{col}_cn" in final_df.columns:
                    original = final_df[col].iloc[0]
                    translated = final_df[f"{col}_cn"].iloc[0]
                    logger.info(f"\n{col} (原文):")
                    logger.info(f"{str(original)[:200]}...")
                    logger.info(f"\n{col}_cn (译文):")
                    logger.info(f"{str(translated)[:200]}...")
        except Exception as e:
            logger.error(f"读取最终结果失败: {e}")
    else:
        logger.error("分块翻译失败")

    logger.info("任务完成!")

if __name__ == "__main__":
    main()