{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 0.13540961408259986, "eval_steps": 500, "global_step": 100, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"completion_length": 1810.6875, "epoch": 0.0013540961408259986, "grad_norm": 1.8862427473068237, "kl": 0.33501143380999565, "learning_rate": 0.0, "loss": 0.0134, "reward": -6.8125, "reward_std": 1.375, "rewards/check_answer": -2.3125, "rewards/check_numbers": -2.4375, "rewards/match_format_approximately": -2.4375, "rewards/match_format_exactly": 0.375, "step": 1}, {"completion_length": 1657.3125, "epoch": 0.002708192281651997, "grad_norm": 2.3477237224578857, "kl": 0.3458457812666893, "learning_rate": 5.000000000000001e-07, "loss": 0.0138, "reward": -4.71875, "reward_std": 4.717200517654419, "rewards/check_answer": -1.65625, "rewards/check_numbers": -1.9375, "rewards/match_format_approximately": -1.875, "rewards/match_format_exactly": 0.75, "step": 2}, {"completion_length": 1846.0, "epoch": 0.004062288422477996, "grad_norm": 2.2645325660705566, "kl": 0.3590291142463684, "learning_rate": 1.0000000000000002e-06, "loss": 0.0144, "reward": -7.5, "reward_std": 0.0, "rewards/check_answer": -2.0, "rewards/check_numbers": -2.5, "rewards/match_format_approximately": -3.0, "rewards/match_format_exactly": 0.0, "step": 3}, {"completion_length": 1727.75, "epoch": 0.005416384563303994, "grad_norm": 0.8347423076629639, "kl": 0.2710846737027168, "learning_rate": 1.5e-06, "loss": 0.0108, "reward": -5.0, "reward_std": 1.0, "rewards/check_answer": -2.15625, "rewards/check_numbers": -2.1875, "rewards/match_format_approximately": -1.59375, "rewards/match_format_exactly": 0.9375, "step": 4}, {"completion_length": 1756.125, "epoch": 0.006770480704129994, "grad_norm": 2.053821086883545, "kl": 0.3353615179657936, "learning_rate": 2.0000000000000003e-06, "loss": 0.0134, "reward": -4.71875, "reward_std": 3.1173267364501953, "rewards/check_answer": -1.65625, "rewards/check_numbers": -1.9375, "rewards/match_format_approximately": -1.875, "rewards/match_format_exactly": 0.75, "step": 5}, {"completion_length": 1721.375, "epoch": 0.008124576844955992, "grad_norm": 1.2479177713394165, "kl": 0.3453684076666832, "learning_rate": 2.5e-06, "loss": 0.0138, "reward": -4.84375, "reward_std": 1.6626253128051758, "rewards/check_answer": -1.78125, "rewards/check_numbers": -1.9375, "rewards/match_format_approximately": -1.875, "rewards/match_format_exactly": 0.75, "step": 6}, {"completion_length": 1757.0625, "epoch": 0.009478672985781991, "grad_norm": 3.2157747745513916, "kl": 0.3393590524792671, "learning_rate": 3e-06, "loss": 0.0136, "reward": -5.5, "reward_std": 4.0, "rewards/check_answer": -2.125, "rewards/check_numbers": -2.25, "rewards/match_format_approximately": -1.875, "rewards/match_format_exactly": 0.75, "step": 7}, {"completion_length": 1687.125, "epoch": 0.010832769126607989, "grad_norm": 1.257407784461975, "kl": 0.2896548956632614, "learning_rate": 3.5e-06, "loss": 0.0116, "reward": -5.25, "reward_std": 1.2886751294136047, "rewards/check_answer": -2.40625, "rewards/check_numbers": -2.1875, "rewards/match_format_approximately": -1.59375, "rewards/match_format_exactly": 0.9375, "step": 8}, {"completion_length": 1573.4375, "epoch": 0.012186865267433988, "grad_norm": 7.496131420135498, "kl": 0.4651091694831848, "learning_rate": 4.000000000000001e-06, "loss": 0.0186, "reward": -3.5, "reward_std": 3.154700517654419, "rewards/check_answer": -2.25, "rewards/check_numbers": -2.0, "rewards/match_format_approximately": -0.75, "rewards/match_format_exactly": 1.5, "step": 9}, {"completion_length": 1713.8125, "epoch": 0.013540961408259987, "grad_norm": 12.320290565490723, "kl": 0.47256777808070183, "learning_rate": 4.5e-06, "loss": 0.0189, "reward": -3.96875, "reward_std": 5.18034553527832, "rewards/check_answer": -1.4375, "rewards/check_numbers": -1.875, "rewards/match_format_approximately": -1.59375, "rewards/match_format_exactly": 0.9375, "step": 10}, {"completion_length": 1770.1875, "epoch": 0.014895057549085985, "grad_norm": 3.1577210426330566, "kl": 0.3338703513145447, "learning_rate": 5e-06, "loss": 0.0134, "reward": -6.5, "reward_std": 1.154700517654419, "rewards/check_answer": -2.0625, "rewards/check_numbers": -2.375, "rewards/match_format_approximately": -2.4375, "rewards/match_format_exactly": 0.375, "step": 11}, {"completion_length": 1831.625, "epoch": 0.016249153689911984, "grad_norm": 1.2170488834381104, "kl": 0.31945526599884033, "learning_rate": 4.944444444444445e-06, "loss": 0.0128, "reward": -6.5, "reward_std": 1.154700517654419, "rewards/check_answer": -2.0625, "rewards/check_numbers": -2.375, "rewards/match_format_approximately": -2.4375, "rewards/match_format_exactly": 0.375, "step": 12}, {"completion_length": 1674.8125, "epoch": 0.017603249830737983, "grad_norm": 1.3876172304153442, "kl": 0.3060573637485504, "learning_rate": 4.888888888888889e-06, "loss": 0.0122, "reward": -2.09375, "reward_std": 4.916364789009094, "rewards/check_answer": -1.78125, "rewards/check_numbers": -1.0625, "rewards/match_format_approximately": -0.75, "rewards/match_format_exactly": 1.5, "step": 13}, {"completion_length": 1753.75, "epoch": 0.018957345971563982, "grad_norm": 4.525331974029541, "kl": 0.3468434661626816, "learning_rate": 4.833333333333333e-06, "loss": 0.0139, "reward": -4.34375, "reward_std": 3.8673267364501953, "rewards/check_answer": -1.375, "rewards/check_numbers": -1.9375, "rewards/match_format_approximately": -1.78125, "rewards/match_format_exactly": 0.75, "step": 14}, {"completion_length": 1731.5625, "epoch": 0.020311442112389978, "grad_norm": 53.159873962402344, "kl": 1.0505033358931541, "learning_rate": 4.777777777777778e-06, "loss": 0.042, "reward": -4.65625, "reward_std": 3.3125, "rewards/check_answer": -1.90625, "rewards/check_numbers": -2.1875, "rewards/match_format_approximately": -1.5, "rewards/match_format_exactly": 0.9375, "step": 15}, {"completion_length": 1754.5, "epoch": 0.021665538253215978, "grad_norm": 1.5012905597686768, "kl": 0.3030897453427315, "learning_rate": 4.722222222222222e-06, "loss": 0.0121, "reward": -6.625, "reward_std": 1.75, "rewards/check_answer": -2.1875, "rewards/check_numbers": -2.375, "rewards/match_format_approximately": -2.4375, "rewards/match_format_exactly": 0.375, "step": 16}, {"completion_length": 1780.875, "epoch": 0.023019634394041977, "grad_norm": 3.191736936569214, "kl": 0.30977538228034973, "learning_rate": 4.666666666666667e-06, "loss": 0.0124, "reward": -6.125, "reward_std": 2.75, "rewards/check_answer": -2.21875, "rewards/check_numbers": -2.3125, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 17}, {"completion_length": 1758.0, "epoch": 0.024373730534867976, "grad_norm": 2.859952211380005, "kl": 0.3292202092707157, "learning_rate": 4.611111111111112e-06, "loss": 0.0132, "reward": -5.03125, "reward_std": 3.4714515209198, "rewards/check_answer": -1.75, "rewards/check_numbers": -1.6875, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 18}, {"completion_length": 1763.125, "epoch": 0.025727826675693975, "grad_norm": 1.9779211282730103, "kl": 0.31487929075956345, "learning_rate": 4.555555555555556e-06, "loss": 0.0126, "reward": -4.15625, "reward_std": 4.521420001983643, "rewards/check_answer": -0.875, "rewards/check_numbers": -1.6875, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 19}, {"completion_length": 1739.9375, "epoch": 0.027081922816519974, "grad_norm": 2.396036148071289, "kl": 0.28815683349967003, "learning_rate": 4.5e-06, "loss": 0.0115, "reward": -5.75, "reward_std": 2.5, "rewards/check_answer": -1.84375, "rewards/check_numbers": -2.3125, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 20}, {"completion_length": 1767.75, "epoch": 0.02843601895734597, "grad_norm": 1.6663864850997925, "kl": 0.3731837794184685, "learning_rate": 4.444444444444444e-06, "loss": 0.0149, "reward": -6.5, "reward_std": 1.154700517654419, "rewards/check_answer": -2.0625, "rewards/check_numbers": -2.375, "rewards/match_format_approximately": -2.4375, "rewards/match_format_exactly": 0.375, "step": 21}, {"completion_length": 1737.375, "epoch": 0.02979011509817197, "grad_norm": 1.7654951810836792, "kl": 0.2803265415132046, "learning_rate": 4.388888888888889e-06, "loss": 0.0112, "reward": -5.5, "reward_std": 2.0, "rewards/check_answer": -2.125, "rewards/check_numbers": -2.25, "rewards/match_format_approximately": -1.875, "rewards/match_format_exactly": 0.75, "step": 22}, {"completion_length": 1775.125, "epoch": 0.03114421123899797, "grad_norm": 2.5995705127716064, "kl": 0.3091626688838005, "learning_rate": 4.333333333333334e-06, "loss": 0.0124, "reward": -6.0, "reward_std": 1.0, "rewards/check_answer": -2.09375, "rewards/check_numbers": -2.3125, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 23}, {"completion_length": 1778.0, "epoch": 0.03249830737982397, "grad_norm": 2.0226786136627197, "kl": 0.3624001517891884, "learning_rate": 4.277777777777778e-06, "loss": 0.0145, "reward": -5.15625, "reward_std": 2.6875, "rewards/check_answer": -2.125, "rewards/check_numbers": -2.09375, "rewards/match_format_approximately": -1.6875, "rewards/match_format_exactly": 0.75, "step": 24}, {"completion_length": 1765.9375, "epoch": 0.033852403520649964, "grad_norm": 1.6428565979003906, "kl": 0.2849075049161911, "learning_rate": 4.222222222222223e-06, "loss": 0.0114, "reward": -5.5, "reward_std": 3.154700517654419, "rewards/check_answer": -2.125, "rewards/check_numbers": -2.25, "rewards/match_format_approximately": -1.875, "rewards/match_format_exactly": 0.75, "step": 25}, {"completion_length": 1684.25, "epoch": 0.035206499661475966, "grad_norm": 22.030235290527344, "kl": 0.8670893646776676, "learning_rate": 4.166666666666667e-06, "loss": 0.0347, "reward": -5.5, "reward_std": 2.0, "rewards/check_answer": -2.125, "rewards/check_numbers": -2.25, "rewards/match_format_approximately": -1.875, "rewards/match_format_exactly": 0.75, "step": 26}, {"completion_length": 1833.6875, "epoch": 0.03656059580230196, "grad_norm": 4.652929782867432, "kl": 0.395771324634552, "learning_rate": 4.111111111111111e-06, "loss": 0.0158, "reward": -6.3125, "reward_std": 1.7368932366371155, "rewards/check_answer": -2.34375, "rewards/check_numbers": -2.375, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 27}, {"completion_length": 1793.75, "epoch": 0.037914691943127965, "grad_norm": 1.0503425598144531, "kl": 0.2645224817097187, "learning_rate": 4.055555555555556e-06, "loss": 0.0106, "reward": -6.25, "reward_std": 2.5, "rewards/check_answer": -1.8125, "rewards/check_numbers": -2.375, "rewards/match_format_approximately": -2.4375, "rewards/match_format_exactly": 0.375, "step": 28}, {"completion_length": 1754.1875, "epoch": 0.03926878808395396, "grad_norm": 1.391834020614624, "kl": 0.25991372019052505, "learning_rate": 4.000000000000001e-06, "loss": 0.0104, "reward": -6.15625, "reward_std": 1.6875, "rewards/check_answer": -2.34375, "rewards/check_numbers": -2.3125, "rewards/match_format_approximately": -2.0625, "rewards/match_format_exactly": 0.5625, "step": 29}, {"completion_length": 1833.625, "epoch": 0.040622884224779957, "grad_norm": 1.7369493246078491, "kl": 0.25668811798095703, "learning_rate": 3.944444444444445e-06, "loss": 0.0103, "reward": -6.15625, "reward_std": 1.842200517654419, "rewards/check_answer": -2.0625, "rewards/check_numbers": -2.21875, "rewards/match_format_approximately": -2.25, "rewards/match_format_exactly": 0.375, "step": 30}, {"completion_length": 1633.4375, "epoch": 0.04197698036560596, "grad_norm": 1.7194873094558716, "kl": 0.3126263990998268, "learning_rate": 3.88888888888889e-06, "loss": 0.0125, "reward": -4.78125, "reward_std": 1.9375, "rewards/check_answer": -2.5625, "rewards/check_numbers": -2.125, "rewards/match_format_approximately": -1.21875, "rewards/match_format_exactly": 1.125, "step": 31}, {"completion_length": 1638.9375, "epoch": 0.043331076506431955, "grad_norm": 1.7313522100448608, "kl": 0.3565537631511688, "learning_rate": 3.833333333333334e-06, "loss": 0.0143, "reward": -3.5, "reward_std": 3.154700517654419, "rewards/check_answer": -2.25, "rewards/check_numbers": -2.0, "rewards/match_format_approximately": -0.75, "rewards/match_format_exactly": 1.5, "step": 32}, {"completion_length": 1835.25, "epoch": 0.04468517264725796, "grad_norm": 1.1694135665893555, "kl": 0.2642197236418724, "learning_rate": 3.777777777777778e-06, "loss": 0.0106, "reward": -6.90625, "reward_std": 1.1875, "rewards/check_answer": -2.03125, "rewards/check_numbers": -2.4375, "rewards/match_format_approximately": -2.625, "rewards/match_format_exactly": 0.1875, "step": 33}, {"completion_length": 1725.5, "epoch": 0.046039268788083954, "grad_norm": 1.1449733972549438, "kl": 0.3033062517642975, "learning_rate": 3.7222222222222225e-06, "loss": 0.0121, "reward": -3.59375, "reward_std": 2.9181928634643555, "rewards/check_answer": -1.59375, "rewards/check_numbers": -1.8125, "rewards/match_format_approximately": -1.3125, "rewards/match_format_exactly": 1.125, "step": 34}, {"completion_length": 1829.875, "epoch": 0.04739336492890995, "grad_norm": 0.6881007552146912, "kl": 0.18399254977703094, "learning_rate": 3.6666666666666666e-06, "loss": 0.0074, "reward": -7.0, "reward_std": 1.0, "rewards/check_answer": -2.03125, "rewards/check_numbers": -2.4375, "rewards/match_format_approximately": -2.71875, "rewards/match_format_exactly": 0.1875, "step": 35}, {"completion_length": 1835.1875, "epoch": 0.04874746106973595, "grad_norm": 10.041669845581055, "kl": 0.496965728700161, "learning_rate": 3.6111111111111115e-06, "loss": 0.0199, "reward": -7.0, "reward_std": 1.0, "rewards/check_answer": -2.03125, "rewards/check_numbers": -2.4375, "rewards/match_format_approximately": -2.71875, "rewards/match_format_exactly": 0.1875, "step": 36}, {"completion_length": 1732.75, "epoch": 0.05010155721056195, "grad_norm": 3.8063204288482666, "kl": 0.3778441846370697, "learning_rate": 3.555555555555556e-06, "loss": 0.0151, "reward": -6.0, "reward_std": 1.0, "rewards/check_answer": -2.09375, "rewards/check_numbers": -2.3125, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 37}, {"completion_length": 1693.75, "epoch": 0.05145565335138795, "grad_norm": 1.3650667667388916, "kl": 0.30275215953588486, "learning_rate": 3.5e-06, "loss": 0.0121, "reward": -3.71875, "reward_std": 4.729746580123901, "rewards/check_answer": -1.71875, "rewards/check_numbers": -1.8125, "rewards/match_format_approximately": -1.3125, "rewards/match_format_exactly": 1.125, "step": 38}, {"completion_length": 1620.125, "epoch": 0.052809749492213946, "grad_norm": 1.0282151699066162, "kl": 0.2538061998784542, "learning_rate": 3.444444444444445e-06, "loss": 0.0102, "reward": -4.71875, "reward_std": 3.1173267364501953, "rewards/check_answer": -1.65625, "rewards/check_numbers": -1.9375, "rewards/match_format_approximately": -1.875, "rewards/match_format_exactly": 0.75, "step": 39}, {"completion_length": 1740.125, "epoch": 0.05416384563303995, "grad_norm": 1.517547607421875, "kl": 0.28775862231850624, "learning_rate": 3.3888888888888893e-06, "loss": 0.0115, "reward": -6.0, "reward_std": 1.0, "rewards/check_answer": -2.09375, "rewards/check_numbers": -2.3125, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 40}, {"completion_length": 1760.875, "epoch": 0.055517941773865945, "grad_norm": 1.3423422574996948, "kl": 0.25366200134158134, "learning_rate": 3.3333333333333333e-06, "loss": 0.0101, "reward": -3.6875, "reward_std": 5.458920001983643, "rewards/check_answer": -0.9375, "rewards/check_numbers": -1.625, "rewards/match_format_approximately": -1.875, "rewards/match_format_exactly": 0.75, "step": 41}, {"completion_length": 1764.625, "epoch": 0.05687203791469194, "grad_norm": 1.0477930307388306, "kl": 0.24418141320347786, "learning_rate": 3.277777777777778e-06, "loss": 0.0098, "reward": -6.125, "reward_std": 1.904700517654419, "rewards/check_answer": -2.21875, "rewards/check_numbers": -2.3125, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 42}, {"completion_length": 1804.3125, "epoch": 0.05822613405551794, "grad_norm": 0.8462934494018555, "kl": 0.2206292524933815, "learning_rate": 3.2222222222222227e-06, "loss": 0.0088, "reward": -5.75, "reward_std": 2.654700517654419, "rewards/check_answer": -1.84375, "rewards/check_numbers": -2.3125, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 43}, {"completion_length": 1714.5625, "epoch": 0.05958023019634394, "grad_norm": 4.159688949584961, "kl": 0.40073226392269135, "learning_rate": 3.1666666666666667e-06, "loss": 0.016, "reward": -6.0, "reward_std": 3.0, "rewards/check_answer": -2.09375, "rewards/check_numbers": -2.3125, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 44}, {"completion_length": 1799.0, "epoch": 0.06093432633716994, "grad_norm": 1.3790596723556519, "kl": 0.3251093700528145, "learning_rate": 3.1111111111111116e-06, "loss": 0.013, "reward": -4.34375, "reward_std": 3.912941098213196, "rewards/check_answer": -1.15625, "rewards/check_numbers": -1.6875, "rewards/match_format_approximately": -2.0625, "rewards/match_format_exactly": 0.5625, "step": 45}, {"completion_length": 1730.5, "epoch": 0.06228842247799594, "grad_norm": 1.2136822938919067, "kl": 0.2775292620062828, "learning_rate": 3.055555555555556e-06, "loss": 0.0111, "reward": -5.4375, "reward_std": 1.5153881907463074, "rewards/check_answer": -2.25, "rewards/check_numbers": -2.15625, "rewards/match_format_approximately": -1.78125, "rewards/match_format_exactly": 0.75, "step": 46}, {"completion_length": 1532.4375, "epoch": 0.06364251861882193, "grad_norm": 1.810671091079712, "kl": 0.33188795298337936, "learning_rate": 3e-06, "loss": 0.0133, "reward": -5.0, "reward_std": 3.0, "rewards/check_answer": -2.15625, "rewards/check_numbers": -2.1875, "rewards/match_format_approximately": -1.59375, "rewards/match_format_exactly": 0.9375, "step": 47}, {"completion_length": 1725.375, "epoch": 0.06499661475964794, "grad_norm": 1.3443578481674194, "kl": 0.24475547671318054, "learning_rate": 2.944444444444445e-06, "loss": 0.0098, "reward": -6.625, "reward_std": 1.0307763814926147, "rewards/check_answer": -2.1875, "rewards/check_numbers": -2.375, "rewards/match_format_approximately": -2.4375, "rewards/match_format_exactly": 0.375, "step": 48}, {"completion_length": 1750.4375, "epoch": 0.06635071090047394, "grad_norm": 5.365706443786621, "kl": 0.4000681936740875, "learning_rate": 2.888888888888889e-06, "loss": 0.016, "reward": -3.5625, "reward_std": 5.708920001983643, "rewards/check_answer": -1.34375, "rewards/check_numbers": -1.5625, "rewards/match_format_approximately": -1.59375, "rewards/match_format_exactly": 0.9375, "step": 49}, {"completion_length": 1709.5, "epoch": 0.06770480704129993, "grad_norm": 1.1483596563339233, "kl": 0.2563626356422901, "learning_rate": 2.8333333333333335e-06, "loss": 0.0103, "reward": -4.0625, "reward_std": 4.8590298891067505, "rewards/check_answer": -1.53125, "rewards/check_numbers": -1.875, "rewards/match_format_approximately": -1.59375, "rewards/match_format_exactly": 0.9375, "step": 50}, {"completion_length": 1766.875, "epoch": 0.06905890318212593, "grad_norm": 1.2646633386611938, "kl": 0.28929753974080086, "learning_rate": 2.7777777777777783e-06, "loss": 0.0116, "reward": -5.71875, "reward_std": 3.5625, "rewards/check_answer": -1.59375, "rewards/check_numbers": -2.0625, "rewards/match_format_approximately": -2.4375, "rewards/match_format_exactly": 0.375, "step": 51}, {"completion_length": 1672.3125, "epoch": 0.07041299932295193, "grad_norm": 1.4798158407211304, "kl": 0.24379675835371017, "learning_rate": 2.7222222222222224e-06, "loss": 0.0098, "reward": -4.5, "reward_std": 3.154700517654419, "rewards/check_answer": -2.1875, "rewards/check_numbers": -2.125, "rewards/match_format_approximately": -1.3125, "rewards/match_format_exactly": 1.125, "step": 52}, {"completion_length": 1846.0, "epoch": 0.07176709546377792, "grad_norm": 0.499040424823761, "kl": 0.23384113609790802, "learning_rate": 2.666666666666667e-06, "loss": 0.0094, "reward": -7.5, "reward_std": 0.0, "rewards/check_answer": -2.0, "rewards/check_numbers": -2.5, "rewards/match_format_approximately": -3.0, "rewards/match_format_exactly": 0.0, "step": 53}, {"completion_length": 1667.625, "epoch": 0.07312119160460392, "grad_norm": 2.6151483058929443, "kl": 0.2990986779332161, "learning_rate": 2.6111111111111113e-06, "loss": 0.012, "reward": -3.90625, "reward_std": 0.8125, "rewards/check_answer": -2.21875, "rewards/check_numbers": -2.0625, "rewards/match_format_approximately": -0.9375, "rewards/match_format_exactly": 1.3125, "step": 54}, {"completion_length": 1818.4375, "epoch": 0.07447528774542993, "grad_norm": 0.8070287704467773, "kl": 0.2465079091489315, "learning_rate": 2.5555555555555557e-06, "loss": 0.0099, "reward": -6.21875, "reward_std": 2.5625, "rewards/check_answer": -1.5625, "rewards/check_numbers": -2.125, "rewards/match_format_approximately": -2.71875, "rewards/match_format_exactly": 0.1875, "step": 55}, {"completion_length": 1587.6875, "epoch": 0.07582938388625593, "grad_norm": 1.9786986112594604, "kl": 0.2781742066144943, "learning_rate": 2.5e-06, "loss": 0.0111, "reward": -4.8125, "reward_std": 3.1792476177215576, "rewards/check_answer": -2.28125, "rewards/check_numbers": -1.875, "rewards/match_format_approximately": -1.59375, "rewards/match_format_exactly": 0.9375, "step": 56}, {"completion_length": 1734.875, "epoch": 0.07718348002708192, "grad_norm": 2.3175973892211914, "kl": 0.31060001626610756, "learning_rate": 2.4444444444444447e-06, "loss": 0.0124, "reward": -4.4375, "reward_std": 3.9589200019836426, "rewards/check_answer": -1.15625, "rewards/check_numbers": -1.6875, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 57}, {"completion_length": 1843.4375, "epoch": 0.07853757616790792, "grad_norm": 1.5155963897705078, "kl": 0.3122865781188011, "learning_rate": 2.388888888888889e-06, "loss": 0.0125, "reward": -7.0, "reward_std": 1.0, "rewards/check_answer": -2.03125, "rewards/check_numbers": -2.4375, "rewards/match_format_approximately": -2.71875, "rewards/match_format_exactly": 0.1875, "step": 58}, {"completion_length": 1836.875, "epoch": 0.07989167230873392, "grad_norm": 4.581693172454834, "kl": 0.3249429166316986, "learning_rate": 2.3333333333333336e-06, "loss": 0.013, "reward": -7.25, "reward_std": 0.5, "rewards/check_answer": -2.0, "rewards/check_numbers": -2.4375, "rewards/match_format_approximately": -2.8125, "rewards/match_format_exactly": 0.0, "step": 59}, {"completion_length": 1779.0625, "epoch": 0.08124576844955991, "grad_norm": 0.9351826906204224, "kl": 0.22926707193255424, "learning_rate": 2.277777777777778e-06, "loss": 0.0092, "reward": -4.25, "reward_std": 4.333920001983643, "rewards/check_answer": -1.28125, "rewards/check_numbers": -1.375, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 60}, {"completion_length": 1728.3125, "epoch": 0.08259986459038592, "grad_norm": 1.1567604541778564, "kl": 0.24363698065280914, "learning_rate": 2.222222222222222e-06, "loss": 0.0097, "reward": -3.71875, "reward_std": 4.4203455448150635, "rewards/check_answer": -1.71875, "rewards/check_numbers": -1.8125, "rewards/match_format_approximately": -1.3125, "rewards/match_format_exactly": 1.125, "step": 61}, {"completion_length": 1778.0, "epoch": 0.08395396073121192, "grad_norm": 1.440003752708435, "kl": 0.2696276009082794, "learning_rate": 2.166666666666667e-06, "loss": 0.0108, "reward": -5.625, "reward_std": 3.75, "rewards/check_answer": -2.25, "rewards/check_numbers": -2.25, "rewards/match_format_approximately": -1.875, "rewards/match_format_exactly": 0.75, "step": 62}, {"completion_length": 1586.3125, "epoch": 0.08530805687203792, "grad_norm": 1.0559355020523071, "kl": 0.274571530520916, "learning_rate": 2.1111111111111114e-06, "loss": 0.011, "reward": -4.75, "reward_std": 1.5, "rewards/check_answer": -1.90625, "rewards/check_numbers": -2.1875, "rewards/match_format_approximately": -1.59375, "rewards/match_format_exactly": 0.9375, "step": 63}, {"completion_length": 1748.5, "epoch": 0.08666215301286391, "grad_norm": 1.2632384300231934, "kl": 0.26701998338103294, "learning_rate": 2.0555555555555555e-06, "loss": 0.0107, "reward": -5.0, "reward_std": 3.309401035308838, "rewards/check_answer": -2.15625, "rewards/check_numbers": -2.1875, "rewards/match_format_approximately": -1.59375, "rewards/match_format_exactly": 0.9375, "step": 64}, {"completion_length": 1783.0625, "epoch": 0.08801624915368991, "grad_norm": 2.7679641246795654, "kl": 0.29364874958992004, "learning_rate": 2.0000000000000003e-06, "loss": 0.0117, "reward": -5.40625, "reward_std": 4.1875, "rewards/check_answer": -1.75, "rewards/check_numbers": -2.0625, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 65}, {"completion_length": 1597.1875, "epoch": 0.08937034529451592, "grad_norm": 1.2754765748977661, "kl": 0.2141249105334282, "learning_rate": 1.944444444444445e-06, "loss": 0.0086, "reward": -2.75, "reward_std": 5.438835144042969, "rewards/check_answer": -1.375, "rewards/check_numbers": -1.1875, "rewards/match_format_approximately": -1.3125, "rewards/match_format_exactly": 1.125, "step": 66}, {"completion_length": 1824.875, "epoch": 0.0907244414353419, "grad_norm": 4.5533366203308105, "kl": 0.3730548955500126, "learning_rate": 1.888888888888889e-06, "loss": 0.0149, "reward": -6.71875, "reward_std": 1.5625, "rewards/check_answer": -2.3125, "rewards/check_numbers": -2.4375, "rewards/match_format_approximately": -2.34375, "rewards/match_format_exactly": 0.375, "step": 67}, {"completion_length": 1747.6875, "epoch": 0.09207853757616791, "grad_norm": 1.273439884185791, "kl": 0.21856291592121124, "learning_rate": 1.8333333333333333e-06, "loss": 0.0087, "reward": -5.75, "reward_std": 2.654700517654419, "rewards/check_answer": -2.375, "rewards/check_numbers": -2.25, "rewards/match_format_approximately": -1.875, "rewards/match_format_exactly": 0.75, "step": 68}, {"completion_length": 1743.6875, "epoch": 0.09343263371699391, "grad_norm": 1.3592967987060547, "kl": 0.2759791202843189, "learning_rate": 1.777777777777778e-06, "loss": 0.011, "reward": -5.5, "reward_std": 3.154700517654419, "rewards/check_answer": -2.125, "rewards/check_numbers": -2.25, "rewards/match_format_approximately": -1.875, "rewards/match_format_exactly": 0.75, "step": 69}, {"completion_length": 1774.5625, "epoch": 0.0947867298578199, "grad_norm": 0.9385823011398315, "kl": 0.1975543610751629, "learning_rate": 1.7222222222222224e-06, "loss": 0.0079, "reward": -5.21875, "reward_std": 3.717200517654419, "rewards/check_answer": -1.625, "rewards/check_numbers": -2.0, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 70}, {"completion_length": 1787.6875, "epoch": 0.0961408259986459, "grad_norm": 3.3899478912353516, "kl": 0.34637799486517906, "learning_rate": 1.6666666666666667e-06, "loss": 0.0139, "reward": -5.28125, "reward_std": 3.6498323678970337, "rewards/check_answer": -1.59375, "rewards/check_numbers": -1.90625, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.375, "step": 71}, {"completion_length": 1685.4375, "epoch": 0.0974949221394719, "grad_norm": 2.660548210144043, "kl": 0.3011273331940174, "learning_rate": 1.6111111111111113e-06, "loss": 0.012, "reward": -5.21875, "reward_std": 4.5625, "rewards/check_answer": -1.625, "rewards/check_numbers": -2.0, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 72}, {"completion_length": 1636.75, "epoch": 0.0988490182802979, "grad_norm": 1.4693210124969482, "kl": 0.28918052092194557, "learning_rate": 1.5555555555555558e-06, "loss": 0.0116, "reward": -3.65625, "reward_std": 3.7013449668884277, "rewards/check_answer": -1.6875, "rewards/check_numbers": -2.0625, "rewards/match_format_approximately": -1.03125, "rewards/match_format_exactly": 1.125, "step": 73}, {"completion_length": 1680.375, "epoch": 0.1002031144211239, "grad_norm": 1.4909543991088867, "kl": 0.3222898282110691, "learning_rate": 1.5e-06, "loss": 0.0129, "reward": -4.0, "reward_std": 2.154700517654419, "rewards/check_answer": -2.21875, "rewards/check_numbers": -2.0625, "rewards/match_format_approximately": -1.03125, "rewards/match_format_exactly": 1.3125, "step": 74}, {"completion_length": 1774.75, "epoch": 0.1015572105619499, "grad_norm": 1.2784687280654907, "kl": 0.2540739066898823, "learning_rate": 1.4444444444444445e-06, "loss": 0.0102, "reward": -5.21875, "reward_std": 4.5625, "rewards/check_answer": -1.625, "rewards/check_numbers": -2.0, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 75}, {"completion_length": 1763.125, "epoch": 0.1029113067027759, "grad_norm": 0.9027302861213684, "kl": 0.23724448308348656, "learning_rate": 1.3888888888888892e-06, "loss": 0.0095, "reward": -5.21875, "reward_std": 2.1173267364501953, "rewards/check_answer": -1.625, "rewards/check_numbers": -2.0, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 76}, {"completion_length": 1733.875, "epoch": 0.10426540284360189, "grad_norm": 1.6776248216629028, "kl": 0.2764371819794178, "learning_rate": 1.3333333333333334e-06, "loss": 0.0111, "reward": -3.96875, "reward_std": 4.674627661705017, "rewards/check_answer": -1.6875, "rewards/check_numbers": -1.8125, "rewards/match_format_approximately": -1.40625, "rewards/match_format_exactly": 0.9375, "step": 77}, {"completion_length": 1552.25, "epoch": 0.10561949898442789, "grad_norm": 1.4731532335281372, "kl": 0.30891193449497223, "learning_rate": 1.2777777777777779e-06, "loss": 0.0124, "reward": -3.5, "reward_std": 4.309401035308838, "rewards/check_answer": -2.25, "rewards/check_numbers": -2.0, "rewards/match_format_approximately": -0.75, "rewards/match_format_exactly": 1.5, "step": 78}, {"completion_length": 1747.8125, "epoch": 0.1069735951252539, "grad_norm": 9.138569831848145, "kl": 0.48863524943590164, "learning_rate": 1.2222222222222223e-06, "loss": 0.0195, "reward": -5.5, "reward_std": 2.0, "rewards/check_answer": -2.125, "rewards/check_numbers": -2.25, "rewards/match_format_approximately": -1.875, "rewards/match_format_exactly": 0.75, "step": 79}, {"completion_length": 1671.625, "epoch": 0.1083276912660799, "grad_norm": 1.7007367610931396, "kl": 0.2873942404985428, "learning_rate": 1.1666666666666668e-06, "loss": 0.0115, "reward": -3.71875, "reward_std": 2.4203455448150635, "rewards/check_answer": -1.71875, "rewards/check_numbers": -1.8125, "rewards/match_format_approximately": -1.3125, "rewards/match_format_exactly": 1.125, "step": 80}, {"completion_length": 1789.4375, "epoch": 0.10968178740690589, "grad_norm": 0.8997422456741333, "kl": 0.2583500109612942, "learning_rate": 1.111111111111111e-06, "loss": 0.0103, "reward": -5.71875, "reward_std": 2.717200517654419, "rewards/check_answer": -1.8125, "rewards/check_numbers": -2.3125, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 81}, {"completion_length": 1769.375, "epoch": 0.11103588354773189, "grad_norm": 4.321206092834473, "kl": 0.34149690717458725, "learning_rate": 1.0555555555555557e-06, "loss": 0.0137, "reward": -3.78125, "reward_std": 4.181081295013428, "rewards/check_answer": -1.8125, "rewards/check_numbers": -1.5, "rewards/match_format_approximately": -1.40625, "rewards/match_format_exactly": 0.9375, "step": 82}, {"completion_length": 1805.0625, "epoch": 0.11238997968855789, "grad_norm": 1.0708001852035522, "kl": 0.2740768678486347, "learning_rate": 1.0000000000000002e-06, "loss": 0.011, "reward": -6.0, "reward_std": 2.154700517654419, "rewards/check_answer": -2.09375, "rewards/check_numbers": -2.3125, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 83}, {"completion_length": 1659.9375, "epoch": 0.11374407582938388, "grad_norm": 1.0681164264678955, "kl": 0.26316771283745766, "learning_rate": 9.444444444444445e-07, "loss": 0.0105, "reward": -3.84375, "reward_std": 2.5932763814926147, "rewards/check_answer": -2.0625, "rewards/check_numbers": -2.0625, "rewards/match_format_approximately": -1.03125, "rewards/match_format_exactly": 1.3125, "step": 84}, {"completion_length": 1779.4375, "epoch": 0.11509817197020988, "grad_norm": 1.5473679304122925, "kl": 0.3125525377690792, "learning_rate": 8.88888888888889e-07, "loss": 0.0125, "reward": -6.5, "reward_std": 2.0, "rewards/check_answer": -2.0625, "rewards/check_numbers": -2.375, "rewards/match_format_approximately": -2.4375, "rewards/match_format_exactly": 0.375, "step": 85}, {"completion_length": 1819.3125, "epoch": 0.11645226811103589, "grad_norm": 2.3900489807128906, "kl": 0.31216056644916534, "learning_rate": 8.333333333333333e-07, "loss": 0.0125, "reward": -7.0, "reward_std": 1.0, "rewards/check_answer": -2.03125, "rewards/check_numbers": -2.4375, "rewards/match_format_approximately": -2.71875, "rewards/match_format_exactly": 0.1875, "step": 86}, {"completion_length": 1738.4375, "epoch": 0.11780636425186188, "grad_norm": 2.0661532878875732, "kl": 0.29267145693302155, "learning_rate": 7.777777777777779e-07, "loss": 0.0117, "reward": -5.5, "reward_std": 2.309401035308838, "rewards/check_answer": -2.125, "rewards/check_numbers": -2.25, "rewards/match_format_approximately": -1.875, "rewards/match_format_exactly": 0.75, "step": 87}, {"completion_length": 1845.8125, "epoch": 0.11916046039268788, "grad_norm": 0.8996082544326782, "kl": 0.2339455522596836, "learning_rate": 7.222222222222222e-07, "loss": 0.0094, "reward": -7.0, "reward_std": 1.0, "rewards/check_answer": -2.03125, "rewards/check_numbers": -2.4375, "rewards/match_format_approximately": -2.71875, "rewards/match_format_exactly": 0.1875, "step": 88}, {"completion_length": 1797.25, "epoch": 0.12051455653351388, "grad_norm": 1.1313228607177734, "kl": 0.257602795958519, "learning_rate": 6.666666666666667e-07, "loss": 0.0103, "reward": -6.125, "reward_std": 2.0307763814926147, "rewards/check_answer": -2.21875, "rewards/check_numbers": -2.3125, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 89}, {"completion_length": 1724.1875, "epoch": 0.12186865267433988, "grad_norm": 1.4940794706344604, "kl": 0.2514024041593075, "learning_rate": 6.111111111111112e-07, "loss": 0.0101, "reward": -6.5, "reward_std": 2.0, "rewards/check_answer": -2.0625, "rewards/check_numbers": -2.375, "rewards/match_format_approximately": -2.4375, "rewards/match_format_exactly": 0.375, "step": 90}, {"completion_length": 1846.0, "epoch": 0.12322274881516587, "grad_norm": 1.5779352188110352, "kl": 0.2609882578253746, "learning_rate": 5.555555555555555e-07, "loss": 0.0104, "reward": -7.5, "reward_std": 0.0, "rewards/check_answer": -2.0, "rewards/check_numbers": -2.5, "rewards/match_format_approximately": -3.0, "rewards/match_format_exactly": 0.0, "step": 91}, {"completion_length": 1792.9375, "epoch": 0.12457684495599188, "grad_norm": 0.8327341675758362, "kl": 0.20343148708343506, "learning_rate": 5.000000000000001e-07, "loss": 0.0081, "reward": -6.0, "reward_std": 2.154700517654419, "rewards/check_answer": -2.09375, "rewards/check_numbers": -2.3125, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 92}, {"completion_length": 1809.25, "epoch": 0.12593094109681788, "grad_norm": 1.7071129083633423, "kl": 0.2567168027162552, "learning_rate": 4.444444444444445e-07, "loss": 0.0103, "reward": -6.21875, "reward_std": 2.5625, "rewards/check_answer": -1.5625, "rewards/check_numbers": -2.125, "rewards/match_format_approximately": -2.71875, "rewards/match_format_exactly": 0.1875, "step": 93}, {"completion_length": 1753.125, "epoch": 0.12728503723764387, "grad_norm": 1.0444004535675049, "kl": 0.21542111784219742, "learning_rate": 3.8888888888888895e-07, "loss": 0.0086, "reward": -5.625, "reward_std": 3.0307763814926147, "rewards/check_answer": -2.25, "rewards/check_numbers": -2.25, "rewards/match_format_approximately": -1.875, "rewards/match_format_exactly": 0.75, "step": 94}, {"completion_length": 1645.1875, "epoch": 0.12863913337846988, "grad_norm": 0.9909619688987732, "kl": 0.21131335943937302, "learning_rate": 3.3333333333333335e-07, "loss": 0.0085, "reward": -4.6875, "reward_std": 3.025252938270569, "rewards/check_answer": -2.6875, "rewards/check_numbers": -1.8125, "rewards/match_format_approximately": -1.3125, "rewards/match_format_exactly": 1.125, "step": 95}, {"completion_length": 1657.375, "epoch": 0.12999322951929587, "grad_norm": 82.79550170898438, "kl": 2.0068226233124733, "learning_rate": 2.7777777777777776e-07, "loss": 0.0803, "reward": -3.78125, "reward_std": 5.41541862487793, "rewards/check_answer": -1.96875, "rewards/check_numbers": -1.8125, "rewards/match_format_approximately": -1.125, "rewards/match_format_exactly": 1.125, "step": 96}, {"completion_length": 1623.8125, "epoch": 0.13134732566012186, "grad_norm": 1.3352768421173096, "kl": 0.2358141951262951, "learning_rate": 2.2222222222222224e-07, "loss": 0.0094, "reward": -3.71875, "reward_std": 4.272027254104614, "rewards/check_answer": -1.71875, "rewards/check_numbers": -1.8125, "rewards/match_format_approximately": -1.3125, "rewards/match_format_exactly": 1.125, "step": 97}, {"completion_length": 1660.625, "epoch": 0.13270142180094788, "grad_norm": 3.638763666152954, "kl": 0.3440277464687824, "learning_rate": 1.6666666666666668e-07, "loss": 0.0138, "reward": -4.09375, "reward_std": 3.475504517555237, "rewards/check_answer": -1.78125, "rewards/check_numbers": -2.125, "rewards/match_format_approximately": -1.3125, "rewards/match_format_exactly": 1.125, "step": 98}, {"completion_length": 1743.9375, "epoch": 0.13405551794177387, "grad_norm": 1.1542189121246338, "kl": 0.2324400395154953, "learning_rate": 1.1111111111111112e-07, "loss": 0.0093, "reward": -4.0625, "reward_std": 4.279700517654419, "rewards/check_answer": -2.59375, "rewards/check_numbers": -1.75, "rewards/match_format_approximately": -1.03125, "rewards/match_format_exactly": 1.3125, "step": 99}, {"completion_length": 1835.9375, "epoch": 0.13540961408259986, "grad_norm": 0.6295638680458069, "kl": 0.21582482755184174, "learning_rate": 5.555555555555556e-08, "loss": 0.0086, "reward": -4.4375, "reward_std": 2.517646074295044, "rewards/check_answer": -1.15625, "rewards/check_numbers": -1.6875, "rewards/match_format_approximately": -2.15625, "rewards/match_format_exactly": 0.5625, "step": 100}], "logging_steps": 1, "max_steps": 100, "num_input_tokens_seen": 0, "num_train_epochs": 1, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 0.0, "train_batch_size": 4, "trial_name": null, "trial_params": null}