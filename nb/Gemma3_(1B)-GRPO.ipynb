{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["To run this, press \"*Runtime*\" and press \"*Run all*\" on a **free** Tesla T4 Google Colab instance!\n", "<div class=\"align-center\">\n", "<a href=\"https://unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "<a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord button.png\" width=\"145\"></a>\n", "<a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a></a> Join Disco<PERSON> if you need help + ⭐ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐\n", "</div>\n", "\n", "To install Unsloth on your own computer, follow the installation instructions on our Github page [here](https://docs.unsloth.ai/get-started/installing-+-updating).\n", "\n", "You will learn how to do [data prep](#Data), how to [train](#Train), how to [run the model](#Inference), & [how to save it](#Save)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### News"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Read our **[TTS Guide](https://docs.unsloth.ai/basics/text-to-speech-tts-fine-tuning)** for instructions and all our notebooks.\n", "\n", "Read our **[Qwen3 Guide](https://docs.unsloth.ai/basics/qwen3-how-to-run-and-fine-tune)** and check out our new **[Dynamic 2.0](https://docs.unsloth.ai/basics/unsloth-dynamic-2.0-ggufs)** quants which outperforms other quantization methods!\n", "\n", "Visit our docs for all our [model uploads](https://docs.unsloth.ai/get-started/all-our-models) and [notebooks](https://docs.unsloth.ai/get-started/unsloth-notebooks).\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": "%%capture\nimport os\nif \"COLAB_\" not in \"\".join(os.environ.keys()):\n    !pip install unsloth vllm\nelse:\n    # [NOTE] Do the below ONLY in Colab! Use [[pip install unsloth vllm]]\n    !pip install --no-deps unsloth vllm==0.8.5.post1"}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": "#@title Colab Extra Install { display-mode: \"form\" }\n%%capture\nimport os\nif \"COLAB_\" not in \"\".join(os.environ.keys()):\n    !pip install unsloth vllm\nelse:\n    !pip install --no-deps unsloth vllm==0.8.5.post1\n    # [NOTE] Do the below ONLY in Colab! Use [[pip install unsloth vllm]]\n    # Skip restarting message in Colab\n    import sys, re, requests; modules = list(sys.modules.keys())\n    for x in modules: sys.modules.pop(x) if \"PIL\" in x or \"google\" in x else None\n    !pip install --no-deps bitsandbytes accelerate xformers==0.0.29.post3 peft \"trl==0.15.2\" triton cut_cross_entropy unsloth_zoo\n    !pip install sentencepiece protobuf \"datasets>=3.4.1\" huggingface_hub hf_transfer\n    \n    # vLLM requirements - vLLM breaks Colab due to reinstalling numpy\n    f = requests.get(\"https://raw.githubusercontent.com/vllm-project/vllm/refs/heads/main/requirements/common.txt\").content\n    with open(\"vllm_requirements.txt\", \"wb\") as file:\n        file.write(re.sub(rb\"(transformers|numpy|xformers)[^\\n]{1,}\\n\", b\"\", f))\n    !pip install -r vllm_requirements.txt"}, {"cell_type": "markdown", "metadata": {"id": "Gd2cpn1kaXRA"}, "source": ["### <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "markdown", "metadata": {"id": "rezoBIE1aXRA"}, "source": ["Load up `Gemma 3 1B Instruct`, and set parameters"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 434, "referenced_widgets": ["983908452fb1423a8b85f99b58a7cb1f", "b67486cb78274f2baa4f9afdc2fd7e3c", "19731cf654e64eb3905f02f4ae277e8c", "b62bf844938f4591a610f6c4ec836464", "7ed0c86e90ca4146a034034e3c295d25", "21f2aec6d7af4c3a82306ad235674829", "fd6ff32a3aa7479e8b5d0cda47819742", "013fb3c914e944b59b24075bbb6d70cf", "859b75a69281413383fc1c0946bf63d0", "d9e72108a08b447ea2a29932391fe429", "21366789f69d431bad2ac2c8d1ec1565", "8d86ec6ac8ea4e0dac38ad19655c2897", "ee082e1dadf74b589840ef533e4d0637", "61d7ca5d56f14d7d93d5cf5e5b712da2", "aea85dac6725482ebac56f11c8e57e34", "bf3141f5d0ae44ecb99707caeb2aaecd", "185b378797334c819f8a199760cac945", "a1fced067e1e40cbb08e25c9377123e0", "c3e88f16997444c490b4950fe5b34e0b", "54c603df4e174b70bd03bce59de287b2", "45aa2714bf0b44e9af7668c674f63863", "8ca7d620dc5d415a83b50f134317d925", "6216e6cebe7b4f6caf4b7a02a3899cf3", "185b6b8280944e1ab7503c305f3d8313", "248a7ac8ad0a4c9c9638502676bf2a91", "ef65703ac8e94c3482dac54b124e8c93", "4c2ccce4364c4ea1b682c378950f964d", "772360d1551141c5ab0a877ae4ed1c76", "b0c51c819fff44c5a6e8e626fec9e937", "c8c4788a0e6a42daa111a96352d245fe", "028216cd27cb4d23880b8577d2234574", "45eda31f22294728bbca360abad799e6", "d7b48dafe15947c9b225681c4a326581", "3fff97801bec4e0fa9fddf5824ae3de5", "851cbec6dad64cc3b1f0a6bec57af9bf", "c83661398fb44a18aa52c6280c3f4b25", "a90ad0da622b401d877873412119e4e0", "f19ac2f749e140b09f5cbf3c4663ef3a", "70a0b21c05e642e5891bb8b91cfb2217", "444aa81458bf4790915c188069506864", "7d4b49f0c54046a89039d25ee7c11f6f", "79980e3b83ce4f35a7858cc8b12c2c2a", "def9dbef26334436a6b2298b5b153f47", "eea838fbfd2c4e82b1769fde1036487e", "0af4f1ee49f746769e108abc15c2df33", "0e90bdc70e8a42b7993c4c5589677040", "625fc21ae1da4e3a998683d62b0a945e", "80c8d8e625fb454881e8c12e2b5725dc", "67f0b8762670467d85d48f99405c8b1f", "551b94fe4b3c4a4f8f3220d013a6d897", "29d36346bc75470eacd30aebf1423e14", "901262ba6fd24d6f90f283301218e6aa", "f029d16fa30a49f098261346df37af1e", "1da84b32592d432fb1a57358ddcdceff", "bbcd20b75ce445bea7be9597efa68d73", "d9902e3bece446928598402ed953585f", "f43ad8be46034c2e9fed4cf23ea6d103", "efaa563da24149aaaa153b7e8c473394", "3f609c16bff04203884e64bde39bac63", "4f313311e3ee4e949580ab8a808561a0", "aaf739645f654cd6a94d897358b2c2e0", "f0c1afb62d8a4616a8df27c9de866916", "1ba6d4a3394e464da1c8cf0f94ca9bce", "6ca6f0f3e43440928ed55839c1780dd9", "671b4725f18a4c809b3c97a65ec9e405", "faa61469d4bc46aebe74d5401d42c3ab", "38db654dd1674055b98958856c87d698", "11f4e6814d4c4fdfae4e629b3d36629a", "f73516b2403f411d925618cfa2af5f46", "d767074c80254322b3062a6c6fe2cd5b", "29a503c15a534923ad510d1c4e88c064", "383d2f04eccd4e38ad791494a06423ed", "36cabdee7cd645a58e1ffc794674a322", "e641631954a14fcfbba1221ba801d53a", "366ae469442b4e3f93d9b074074d69f6", "8029855dfa614963a7f5ff11d48dcbdb", "e74fcfb683074f27806b6aaae329b74d"]}, "id": "DkIvEkIIkEyB", "outputId": "46cac3b1-51e0-4b2d-f12e-b89421ab3c63"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n", "🦥 Unsloth Zoo will now patch everything to make training faster!\n", "INFO 03-19 15:51:40 [__init__.py:256] Automatically detected platform cuda.\n", "==((====))==  Unsloth 2025.3.17: Fast Gemma3 patching. Transformers: 4.50.0.dev0. vLLM: 0.8.0.\n", "   \\\\   /|    Tesla T4. Num GPUs = 1. Max memory: 14.741 GB. Platform: Linux.\n", "O^O/ \\_/ \\    Torch: 2.6.0+cu124. CUDA: 7.5. CUDA Toolkit: 12.4. Triton: 3.2.0\n", "\\        /    Bfloat16 = FALSE. FA [Xformers = 0.0.29.post3. FA2 = False]\n", " \"-____-\"     Free license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n", "Unsloth: Using float16 precision for gemma3 won't work! Using float32.\n", "Unsloth: QLoRA and full finetuning all not selected. Switching to 16bit LoRA.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "983908452fb1423a8b85f99b58a7cb1f", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/2.00G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8d86ec6ac8ea4e0dac38ad19655c2897", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/215 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6216e6cebe7b4f6caf4b7a02a3899cf3", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/1.16M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3fff97801bec4e0fa9fddf5824ae3de5", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.model:   0%|          | 0.00/4.69M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0af4f1ee49f746769e108abc15c2df33", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/33.4M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d9902e3bece446928598402ed953585f", "version_major": 2, "version_minor": 0}, "text/plain": ["added_tokens.json:   0%|          | 0.00/35.0 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "38db654dd1674055b98958856c87d698", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/670 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from unsloth import FastModel\n", "import torch\n", "max_seq_length = 1024\n", "\n", "fourbit_models = [\n", "    # 4bit dynamic quants for superior accuracy and low memory use\n", "    \"unsloth/gemma-3-1b-it-unsloth-bnb-4bit\",\n", "    \"unsloth/gemma-3-4b-it-unsloth-bnb-4bit\",\n", "    \"unsloth/gemma-3-12b-it-unsloth-bnb-4bit\",\n", "    \"unsloth/gemma-3-27b-it-unsloth-bnb-4bit\",\n", "\n", "    # Other popular models!\n", "    \"unsloth/Llama-3.1-8B\",\n", "    \"unsloth/Llama-3.2-3B\",\n", "    \"unsloth/Llama-3.3-70B\",\n", "    \"unsloth/mistral-7b-instruct-v0.3\",\n", "    \"unsloth/Phi-4\",\n", "] # More models at https://huggingface.co/unsloth\n", "\n", "model, tokenizer = FastModel.from_pretrained(\n", "    model_name = \"unsloth/gemma-3-1b-it\",\n", "    max_seq_length = max_seq_length, # Choose any for long context!\n", "    load_in_4bit = False,  # 4 bit quantization to reduce memory\n", "    load_in_8bit = False, # [NEW!] A bit more accurate, uses 2x memory\n", "    full_finetuning = False, # [NEW!] We have full finetuning now!\n", "    # token = \"hf_...\", # use one if using gated models\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "6nHQVsQJ2roh"}, "source": ["We now add LoRA adapters so we only need to update a small amount of parameters!"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "uNuwc5sJ2pYK", "outputId": "390bac89-9b7e-4913-d44e-099249187584"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Unsloth: Making `model.base_model.model.model` require gradients\n"]}], "source": ["model = FastModel.get_peft_model(\n", "    model,\n", "    finetune_vision_layers     = False, # Turn off for just text!\n", "    finetune_language_layers   = True,  # Should leave on!\n", "    finetune_attention_modules = True,  # Attention good for GRPO\n", "    finetune_mlp_modules       = True,  # SHould leave on always!\n", "\n", "    r = 8,           # Larger = higher accuracy, but might overfit\n", "    lora_alpha = 8,  # Recommended alpha == r at least\n", "    lora_dropout = 0,\n", "    bias = \"none\",\n", "    random_state = 3407,\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "7KGgPgk_5S8r"}, "source": ["### Data Prep\n", "<a name=\"Data\"></a>\n", "\n", "We're using OpenAI's famous GSM8K dataset!"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 247, "referenced_widgets": ["04505a0929fd448fb634f5d2517d0143", "7c4cd321445b43bfa818b599d42c8cb7", "50503005b58845b79254f89f95fb03d9", "9da829640aab4d69a8753118e2a42014", "a4013e1c447446f7b45bed913b15351d", "cec948b41ee348fcbdf2f5fa290ffe42", "6d1b19657db94907b6fdf0a3b3a05e84", "8a21cb24786049e2946c9a050a6673ab", "340c07f1b7b14527a378880f9166ef55", "67eda18557dc461483821ffd44a25eb4", "4cabdb178b97464e875d507dd1410bbf", "4699d5847ab34d90a637b5025810e359", "7a55b04eb2d34f36a4ca140ae90c0d96", "116d96200f7d4be5b2c1cd32d9be9ec2", "8b48641e1f41441b8390f5b4f0e3be9b", "9c9123b394c34e82b56f0589389703b4", "669cb00554134e8287d0daae7372d397", "9e7bb56731134d9cba89cbde208358e0", "a7142d54e9d54e94af5cac45f82826fb", "2de092a4e86748fd882ca2bb5cf03c94", "0c96fa0b7ed344f4be44632451b406d7", "bf96f070c2f54468a674f11681fcb22d", "1664b23faa1b4292bb5727bd525c45be", "5017bf753b93420f94bf27eeded7ca79", "099719146cf54e7f828c53605325b3f3", "db5911df3b4e4691ad183c5a90aee102", "a90a59dd2fa844fcb0a4cadfeeb3b702", "cf83a6e558f64ff98276981a82f3b2ac", "0ef32700424c4799b8216de4ed8bbbb9", "cac3cce4717d4b10ae9f6a12468e6528", "62370afab24e49e0b89b34d0907414c8", "a9dbcb0e164544ba8321a7916500009d", "a6030d7b5bbd460fb5aa1356c607ec82", "1f47a0e576414befbf672cd20ac44fa8", "5e4bcd42eb8a4247ac7cc67034b3f415", "ed53e921682348b28e2be40eaf96cbf7", "547512d47e0f43d7a81bb817a3b98b59", "ad09ac69cad24880a0515c1370011c36", "9677dc2a2f4847d89d3ca42c01435205", "645b707de96a4c2eb07eb118db311fb6", "d6bdbc8fc2ee47ab9e01c3f8dff694d4", "c6de3f102e1d4a378d3c2b1c46b07f31", "d1e38bbf7593462bb87b14bacee9e3d9", "846c1dd9b3214c68bbfc362166a6b9ee", "c1401835d0a14d5b8482b92fafc64d49", "88e52f4b8d3a43bab15b441fdf768202", "5686cb44652d4afda84ea8283d232c99", "ecdd7380c74a4cdaaea3d7c94285c5cf", "977b794c5d1b4910a2126193ee27f850", "02d6b8e9a02b470a92f700d9e7fea5d4", "8464d2f310b045808bcd7206aa7c8cc5", "964fcc9cfd8a4e98a13b259e8253fbb3", "6306f01e5e14455f96f1d852ac3b323f", "25c7874a020344e7aad2869f7a27db4e", "86e9660b2d72460c836d9bba348be56b"]}, "id": "zEibULDtlOMU", "outputId": "c9d05c1d-39be-4135-8aa9-814ca1d1c2de"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "04505a0929fd448fb634f5d2517d0143", "version_major": 2, "version_minor": 0}, "text/plain": ["README.md:   0%|          | 0.00/7.94k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4699d5847ab34d90a637b5025810e359", "version_major": 2, "version_minor": 0}, "text/plain": ["train-00000-of-00001.parquet:   0%|          | 0.00/2.31M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1664b23faa1b4292bb5727bd525c45be", "version_major": 2, "version_minor": 0}, "text/plain": ["test-00000-of-00001.parquet:   0%|          | 0.00/419k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1f47a0e576414befbf672cd20ac44fa8", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating train split:   0%|          | 0/7473 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c1401835d0a14d5b8482b92fafc64d49", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating test split:   0%|          | 0/1319 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["Dataset({\n", "    features: ['question', 'answer'],\n", "    num_rows: 7473\n", "})"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from datasets import load_dataset\n", "dataset = load_dataset(\"openai/gsm8k\", \"main\", split = \"train\")\n", "dataset"]}, {"cell_type": "markdown", "metadata": {"id": "GRfa3z_atGgT"}, "source": ["Let's look at the first row:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 52}, "id": "rIRsNJ_YitXl", "outputId": "89801c0c-0034-4a1a-d509-c2e68ef7dbf6"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["'<PERSON> sold clips to 48 of her friends in April, and then she sold half as many clips in May. How many clips did <PERSON> sell altogether in April and May?'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset[0][\"question\"]"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 52}, "id": "B3yJTiFgtKYq", "outputId": "1e4a5bbf-8f67-437b-a89c-e0f262a2a33e"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["'Natalia sold 48/2 = <<48/2=24>>24 clips in May.\\nNatalia sold 48+24 = <<48+24=72>>72 clips altogether in April and May.\\n#### 72'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset[0][\"answer\"]"]}, {"cell_type": "markdown", "metadata": {"id": "yqq9kGuVtIYn"}, "source": ["We notice all answers like about have a ####, so we extract it:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 35}, "id": "_5I3BCkViwuC", "outputId": "e14a21d0-2190-4b6c-edd1-f9df8854b619"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["'72'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["def extract_hash_answer(text):\n", "    if \"####\" not in text: return None\n", "    return text.split(\"####\")[1].strip()\n", "extract_hash_answer(dataset[0][\"answer\"])"]}, {"cell_type": "markdown", "metadata": {"id": "FiFNBLyytPCD"}, "source": ["We now create a system prompt which can be customized. We add 4 extra symbols for working out or thinking / reasoning sections and a final answer:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 52}, "id": "mHjiV3kGi8Y9", "outputId": "96657090-0f4a-4fb0-c079-9b5d8a781557"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["'You are given a problem.\\nThink about the problem and provide your working out.\\nPlace it between <start_working_out> and <end_working_out>.\\nThen, provide your solution between <SOLUTION></SOLUTION>'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["reasoning_start = \"<start_working_out>\"\n", "reasoning_end   = \"<end_working_out>\"\n", "solution_start = \"<SOLUTION>\"\n", "solution_end = \"</SOLUTION>\"\n", "\n", "system_prompt = \\\n", "f\"\"\"You are given a problem.\n", "Think about the problem and provide your working out.\n", "Place it between {reasoning_start} and {reasoning_end}.\n", "Then, provide your solution between {solution_start}{solution_end}\"\"\"\n", "system_prompt"]}, {"cell_type": "markdown", "metadata": {"id": "BFRYlk9ntYTm"}, "source": ["Let's map the dataset! and see the first row:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 224, "referenced_widgets": ["3fabb349d1f943b09ed25784a0d0ab0a", "31c11ed4511b45cd96d2f5580f550113", "8f85cea6a8f2402ca1c49b4c6da61a74", "f6a520e1570d4f7faffc1b6e2c6200d2", "f1d996d267de44c1973df32c354bee97", "6decae16dd5b404ba272d89df9c1372b", "9151ae8e05634cf4a8ce42677f211bac", "ba4ac2596dda42698ae048f9b7a11c61", "393e1a3fd0e24c94b856b6d806012d52", "10c20527dc19466eb4d6eb325529a0df", "8f2974213b954318902d39a1fda9b3fb"]}, "id": "5tkTF5Hmlhl-", "outputId": "102eb06c-bcfa-4319-fd52-96be1d0bbc97"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3fabb349d1f943b09ed25784a0d0ab0a", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/7473 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["{'question': '<PERSON> sold clips to 48 of her friends in April, and then she sold half as many clips in May. How many clips did <PERSON> sell altogether in April and May?',\n", " 'answer': '72',\n", " 'prompt': [{'content': 'You are given a problem.\\nThink about the problem and provide your working out.\\nPlace it between <start_working_out> and <end_working_out>.\\nThen, provide your solution between <SOLUTION></SOLUTION>',\n", "   'role': 'system'},\n", "  {'content': '<PERSON> sold clips to 48 of her friends in April, and then she sold half as many clips in May. How many clips did <PERSON> sell altogether in April and May?',\n", "   'role': 'user'}]}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset = dataset.map(lambda x: {\n", "    \"prompt\" : [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\",   \"content\": x[\"question\"]},\n", "    ],\n", "    \"answer\": extract_hash_answer(x[\"answer\"]),\n", "})\n", "dataset[0]"]}, {"cell_type": "markdown", "metadata": {"id": "w6MsfbGUtja0"}, "source": ["We create a regex format to match the reasoning sections and answers:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "l5X6oDNDn6Zj"}, "outputs": [], "source": ["import re\n", "\n", "match_format = re.compile(\n", "    rf\"^[\\s]{{0,}}\"\\\n", "    rf\"{reasoning_start}.+?{reasoning_end}.*?\"\\\n", "    rf\"{solution_start}(.+?){solution_end}\"\\\n", "    rf\"[\\s]{{0,}}$\",\n", "    flags = re.MULTILINE | re.DOTALL\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "ME3-UVc6tnYP"}, "source": ["We verify it works:"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "LVvrKUBEtoQD", "outputId": "9867d705-35ad-4207-9742-82e0f45fd48b"}, "outputs": [{"data": {"text/plain": ["<re.Match object; span=(0, 71), match='<start_working_out>Let me think!<end_working_out>>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["match_format.search(\n", "    \"<start_working_out>Let me think!<end_working_out>\"\\\n", "    \"<SOLUTION>2</SOLUTION>\",\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "Qglh2OxpuQzK"}, "source": ["We now want to create a reward function to match the format exactly - we reward it with 3 points if it succeeds:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"id": "g8MPYPvvo1ri"}, "outputs": [], "source": ["def match_format_exactly(completions, **kwargs):\n", "    scores = []\n", "    for completion in completions:\n", "        score = 0\n", "        response = completion[0][\"content\"]\n", "        # Match if format is seen exactly!\n", "        if match_format.search(response) is not None: score += 3.0\n", "        scores.append(score)\n", "    return scores"]}, {"cell_type": "markdown", "metadata": {"id": "VqnEZ4msuZyZ"}, "source": ["If it fails, we want to reward the model if it at least follows the format partially, by counting each symbol:"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"id": "1LlYVZjdpij9"}, "outputs": [], "source": ["def match_format_approximately(completions, **kwargs):\n", "    scores = []\n", "    for completion in completions:\n", "        score = 0\n", "        response = completion[0][\"content\"]\n", "        # Count how many keywords are seen - we penalize if too many!\n", "        # If we see 1, then plus some points!\n", "        score += 0.5 if response.count(reasoning_start) == 1 else -0.5\n", "        score += 0.5 if response.count(reasoning_end)   == 1 else -0.5\n", "        score += 0.5 if response.count(solution_start)  == 1 else -0.5\n", "        score += 0.5 if response.count(solution_end)    == 1 else -0.5\n", "        scores.append(score)\n", "    return scores"]}, {"cell_type": "markdown", "metadata": {"id": "CBwDVDxtuhWm"}, "source": ["Finally, we want to extract the generated answer, and reward or penalize it! We also reward it based on how close the answer is to the true one via ratios:"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"id": "jnKYp_IYqFr2"}, "outputs": [], "source": ["def check_answer(prompts, completions, answer, **kwargs):\n", "    question = prompts[0][-1][\"content\"]\n", "    responses = [completion[0][\"content\"] for completion in completions]\n", "\n", "    extracted_responses = [\n", "        guess.group(1)\n", "        if (guess := match_format.search(r)) is not None else None \\\n", "        for r in responses\n", "    ]\n", "\n", "    scores = []\n", "    for guess, true_answer in zip(extracted_responses, answer):\n", "        score = 0\n", "        if guess is None:\n", "            scores.append(0)\n", "            continue\n", "        # Correct answer gets 3 points!\n", "        if guess == true_answer:\n", "            score += 3.0\n", "        # Match if spaces are seen\n", "        elif guess.strip() == true_answer.strip():\n", "            score += 1.5\n", "        else:\n", "            # We also reward it if the answer is close via ratios!\n", "            # Ie if the answer is within some range, reward it!\n", "            try:\n", "                ratio = float(guess) / float(true_answer)\n", "                if   ratio >= 0.9 and ratio <= 1.1: score += 0.5\n", "                elif ratio >= 0.8 and ratio <= 1.2: score += 0.25\n", "                else: score -= 1.0 # Penalize wrong answers\n", "            except:\n", "                score -= 0.5 # Penalize\n", "        scores.append(score)\n", "    return scores"]}, {"cell_type": "markdown", "metadata": {"id": "BvOYCf1Ly83w"}, "source": ["Also sometimes it might not be 1 number as the answer, but like a sentence for example \"The solution is $20\" -> we extract 20."]}, {"cell_type": "code", "execution_count": 16, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "MtFAX3_xy77b", "outputId": "702710ed-2e71-41fb-a4f4-fd024b0d8eb7"}, "outputs": [{"data": {"text/plain": ["['0.34']"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["match_numbers = re.compile(\n", "    rf\"{solution_start}.*?([\\d\\.]{{1,}})\",\n", "    flags = re.MULTILINE | re.DOTALL\n", ")\n", "match_numbers.findall(\"<SOLUTION>  0.34  </SOLUTION>\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"id": "VqWply0z0DrP"}, "outputs": [], "source": ["def check_numbers(prompts, completions, answer, **kwargs):\n", "    question = prompts[0][-1][\"content\"]\n", "    responses = [completion[0][\"content\"] for completion in completions]\n", "\n", "    extracted_responses = [\n", "        guess.group(1)\n", "        if (guess := match_numbers.search(r)) is not None else None \\\n", "        for r in responses\n", "    ]\n", "\n", "    scores = []\n", "    print('*'*20, f\"Question:\\n{question}\", f\"\\nAnswer:\\n{answer[0]}\", f\"\\nResponse:\\n{responses[0]}\", f\"\\nExtracted:\\n{extracted_responses[0]}\")\n", "    for guess, true_answer in zip(extracted_responses, answer):\n", "        if guess is None:\n", "            scores.append(0)\n", "            continue\n", "        # Convert to numbers\n", "        try:\n", "            true_answer = float(true_answer.strip())\n", "            guess       = float(guess.strip())\n", "            scores.append(1.5 if guess == true_answer else 0.0)\n", "        except:\n", "            scores.append(0)\n", "            continue\n", "    return scores"]}, {"cell_type": "markdown", "metadata": {"id": "Ux6iqP7z5YOo"}, "source": ["<a name=\"<PERSON>\"></a>\n", "### Train the model\n", "\n", "Now set up GRPO Trainer and all configurations!"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ptqkXK2D4d6p", "outputId": "68f6455c-201e-45f7-f24c-46be59ff15ad"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Unsloth: We now expect `per_device_train_batch_size` to be a multiple of `num_generations`.\n", "We will change the batch size of 1 to the `num_generations` of 4\n"]}], "source": ["max_prompt_length = 256\n", "\n", "from trl import GRPOConfig, GRPOTrainer\n", "training_args = GRPOConfig(\n", "    learning_rate = 5e-6,\n", "    adam_beta1 = 0.9,\n", "    adam_beta2 = 0.99,\n", "    weight_decay = 0.1,\n", "    warmup_ratio = 0.1,\n", "    lr_scheduler_type = \"cosine\",\n", "    optim = \"adamw_torch_fused\",\n", "    logging_steps = 1,\n", "    per_device_train_batch_size = 1,\n", "    gradient_accumulation_steps = 1, # Increase to 4 for smoother training\n", "    num_generations = 4, # Decrease if out of memory\n", "    max_prompt_length = max_prompt_length,\n", "    max_completion_length = max_seq_length - max_prompt_length,\n", "    # num_train_epochs = 1, # Set to 1 for a full training run\n", "    max_steps = 50,\n", "    save_steps = 50,\n", "    max_grad_norm = 0.1,\n", "    report_to = \"none\", # Can use Weights & Biases\n", "    output_dir = \"outputs\",\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "r9Mv8UZO5hz-"}, "source": ["And let's run the trainer! If you scroll up, you'll see a table of rewards. The goal is to see the `reward` column increase!\n", "\n", "You might have to wait 150 to 200 steps for any action. You'll probably get 0 reward for the first 100 steps. Please be patient!\n", "\n", "| Step | Training Loss | reward    | reward_std | completion_length | kl       |\n", "|------|---------------|-----------|------------|-------------------|----------|\n", "| 1    | 0.000000      | 0.125000  | 0.000000   | 200.000000        | 0.000000 |\n", "| 2    | 0.000000      | 0.072375  | 0.248112   | 200.000000        | 0.000000 |\n", "| 3    | 0.000000      | -0.079000 | 0.163776   | 182.500000        | 0.000005 |\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "vzOuSVCL_GA9", "outputId": "7fed464c-f2b6-4924-e98c-bc1c0a190887"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Unsloth: Switching to float32 training since model cannot work with float16\n"]}, {"name": "stderr", "output_type": "stream", "text": ["==((====))==  Unsloth - 2x faster free finetuning | Num GPUs used = 1\n", "   \\\\   /|    Num examples = 7,473 | Num Epochs = 1 | Total steps = 50\n", "O^O/ \\_/ \\    Batch size per device = 4 | Gradient accumulation steps = 1\n", "\\        /    Data Parallel GPUs = 1 | Total batch size (4 x 1 x 1) = 4\n", " \"-____-\"     Trainable parameters = 6,522,880/1,006,408,832 (0.65% trained)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["******************** Question:\n", "A concert ticket costs $40. Mr. <PERSON> bought 12 tickets and received a 5% discount for every ticket bought that exceeds 10. How much did Mr. <PERSON> pay in all? \n", "Answer:\n", "476 \n", "Response:\n", "<start_working_out>\n", "Let $C$ be the cost of a ticket, which is $C = 40$.\n", "Let $N$ be the number of tickets Mr<PERSON> bought, which is $N = 12$.\n", "The total cost of the tickets without discount is $N \\times C = 12 \\times 40 = 480$.\n", "Mr. <PERSON> received a 5% discount for every ticket bought that exceeds 10.\n", "The number of tickets that exceed 10 is $12 - 10 = 2$.\n", "The discount for each of these tickets is $5\\%$ of $10 = 0.05 \\times 10 = 0.5$.\n", "The discount amount for the 2 tickets is $2 \\times 0.5 = 1$.\n", "The discounted price for each of the 2 tickets is $10 - 0.5 = 9.5$.\n", "The total cost for the 2 tickets is $2 \\times 9.5 = 19$.\n", "The total cost of the tickets is the original cost minus the discount amount: $480 - 19 = 461$.\n", "However, this is incorrect because we need to consider the discounts for the tickets that exceed 10.\n", "Let $x$ be the number of tickets that exceed 10. We have $x = 12 - 10 = 2$.\n", "The discount is 5% for each ticket that exceeds 10.\n", "So the discounted price for each ticket is $40 \\times (1 - 0.05) = 40 \\times 0.95 = 38$.\n", "The total cost for the 2 tickets is $2 \\times 38 = 76$.\n", "The total cost of the 12 tickets is $12 \\times 40 = 480$.\n", "The total discount is $2 \\times 0.05 \\times 12 = 0.1 \\times 12 = 1.2$.\n", "The total cost after discount is $480 - 1.2 = 478.8$.\n", "However, we are given that Mr<PERSON> received a 5% discount for every ticket bought that exceeds 10.\n", "So, we need to calculate the number of tickets that exceed 10.\n", "Mr. <PERSON> bought 12 tickets and received a 5% discount for every ticket bought that exceeds 10.\n", "The number of tickets that exceed 10 is $12 - 10 = 2$.\n", "The discount on each of these 2 tickets is $5\\%$ of $10$, which is $0.05 \\times 10 = 0.5$.\n", "The discount amount for each of the 2 tickets is $0.5 \\times 40 = 20$.\n", "The discounted price for each of the 2 tickets is $40 - 20 = 20$.\n", "The total cost for the 2 tickets is $2 \\times 20 = 40$.\n", "The total cost for the 12 tickets is $12 \\times 40 = 480$.\n", "The total discount is $2 \\times 0.5 = 1$.\n", "The total cost after discount is $480 - 1 = 479$.\n", "But this is not correct.\n", "\n", "Let $N = 12$. The cost per ticket is $40$.\n", "The discount is 5% for \n", "Extracted:\n", "None\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='14' max='50' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [14/50 13:12 < 39:36, 0.02 it/s, Epoch 0.00/1]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "      <th>reward</th>\n", "      <th>reward_std</th>\n", "      <th>completion_length</th>\n", "      <th>kl</th>\n", "      <th>rewards / match_format_exactly</th>\n", "      <th>rewards / match_format_approximately</th>\n", "      <th>rewards / check_answer</th>\n", "      <th>rewards / check_numbers</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>-1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>758.250000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>-1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.000000</td>\n", "      <td>-0.500000</td>\n", "      <td>0.577350</td>\n", "      <td>664.250000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>-0.500000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.000000</td>\n", "      <td>0.875000</td>\n", "      <td>1.181454</td>\n", "      <td>411.000000</td>\n", "      <td>0.000004</td>\n", "      <td>0.000000</td>\n", "      <td>0.500000</td>\n", "      <td>0.000000</td>\n", "      <td>0.375000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.000000</td>\n", "      <td>1.500000</td>\n", "      <td>0.000000</td>\n", "      <td>269.250000</td>\n", "      <td>0.000003</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>155.250000</td>\n", "      <td>0.000009</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.000000</td>\n", "      <td>-1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>666.750000</td>\n", "      <td>0.000006</td>\n", "      <td>0.000000</td>\n", "      <td>-1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.000000</td>\n", "      <td>1.500000</td>\n", "      <td>0.000000</td>\n", "      <td>409.500000</td>\n", "      <td>0.000006</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.000000</td>\n", "      <td>-0.500000</td>\n", "      <td>0.577350</td>\n", "      <td>496.250000</td>\n", "      <td>0.000006</td>\n", "      <td>0.000000</td>\n", "      <td>-0.500000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.000000</td>\n", "      <td>-0.750000</td>\n", "      <td>0.500000</td>\n", "      <td>634.500000</td>\n", "      <td>0.000008</td>\n", "      <td>0.000000</td>\n", "      <td>-0.750000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.000000</td>\n", "      <td>-0.250000</td>\n", "      <td>0.500000</td>\n", "      <td>558.750000</td>\n", "      <td>0.000007</td>\n", "      <td>0.000000</td>\n", "      <td>-0.250000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.000000</td>\n", "      <td>1.500000</td>\n", "      <td>0.000000</td>\n", "      <td>253.750000</td>\n", "      <td>0.000011</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.000000</td>\n", "      <td>-1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>768.000000</td>\n", "      <td>0.000005</td>\n", "      <td>0.000000</td>\n", "      <td>-1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Unsloth: Will smartly offload gradients to save VRAM!\n", "******************** Question:\n", "<PERSON> is trying to decide whether to buy a house or a trailer. A house costs $480,000 and a trailer costs $120,000. Each loan will be paid in monthly installments over 20 years. How much more is the monthly payment on the house compared to the trailer? \n", "Answer:\n", "1500 \n", "Response:\n", "<start_working_out>\n", "We need to calculate the monthly payment on the house and the trailer.\n", "\n", "House cost: $480,000\n", "Loan amount: $480,000\n", "Interest rate: We need to assume an interest rate for this problem. Let's assume an annual interest rate of 6% (this is a common rate).\n", "Loan term: 20 years, so 20 * 12 = 240 months\n", "We will use the loan payment formula: M = P [ i(1 + i)^n ] / [ (1 + i)^n – 1]\n", "where M is the monthly payment, P is the loan amount, i is the monthly interest rate, and n is the number of months.\n", "\n", "Monthly interest rate (i) = Annual interest rate / 12 = 0.06 / 12 = 0.005\n", "Number of months (n) = 240\n", "\n", "M = 480000 [ 0.005(1 + 0.005)^240 ] / [ (1 + 0.005)^240 – 1]\n", "M = 480000 [ 0.005(1.005)^240 ] / [ (1.005)^240 – 1]\n", "M = 480000 [ 0.005 * 3.310853] / [ 3.310853 – 1]\n", "M = 480000 [ 0.016554265] / [2.310853]\n", "M = 480000 * 0.00703658\n", "M = $331.54\n", "\n", "Trailer cost: $120,000\n", "Loan amount: $120,000\n", "Interest rate: We still assume an annual interest rate of 6% (this is a common rate).\n", "Loan term: 20 years, so 20 * 12 = 240 months\n", "We will use the loan payment formula: M = P [ i(1 + i)^n ] / [ (1 + i)^n – 1]\n", "where M is the monthly payment, P is the loan amount, i is the monthly interest rate, and n is the number of months.\n", "\n", "Monthly interest rate (i) = Annual interest rate / 12 = 0.06 / 12 = 0.005\n", "Number of months (n) = 240\n", "\n", "M = 120000 [ 0.005(1 + 0.005)^240 ] / [ (1 + 0.005)^240 – 1]\n", "M = 120000 [ 0.005(1.005)^240 ] / [ (1.005)^240 – 1]\n", "M = 120000 [ 0.005 * 3.310853 ] / [ 3.310853 – 1]\n", "M = 120000 [ 0.016554265] / [2.310853]\n", "M = 1200 \n", "Extracted:\n", "None\n", "******************** Question:\n", "<PERSON> pays $40/hour for 3 hours per week of clarinet lessons and $28/hour for 5 hours a week of piano lessons. How much more does she spend on piano lessons than clarinet lessons in a year? \n", "Answer:\n", "1040 \n", "Response:\n", "<start_working_out>\n", "Let $C$ be the amount <PERSON> spends on clarinet lessons per week, and $P$ be the amount <PERSON> spends on piano lessons per week.\n", "<PERSON> pays $40/hour for 3 hours per week of clarinet lessons, so $C = 40 \\times 3 = 120$.\n", "<PERSON> pays $28/hour for 5 hours a week of piano lessons, so $P = 28 \\times 5 = 140$.\n", "<PERSON>'s weekly spending on clarinet lessons is $120/week$, and her weekly spending on piano lessons is $140/week$.\n", "There are 52 weeks in a year.\n", "The amount she spends on clarinet lessons in a year is $120 \\times 52 = 6240$.\n", "The amount she spends on piano lessons in a year is $140 \\times 52 = 7280$.\n", "The difference between the amount she spends on piano lessons and clarinet lessons in a year is $7280 - 6240 = 1040$.\n", "\n", "<SOLUTION>\n", "The amount she spends on clarinet lessons in a year is $120 \\times 52 = 6240$.\n", "The amount she spends on piano lessons in a year is $140 \\times 52 = 7280$.\n", "The difference between the amount she spends on piano lessons and clarinet lessons in a year is $7280 - 6240 = 1040$.\n", "\n", "Final Answer: The final answer is $\\boxed{1040}$ \n", "Extracted:\n", "120\n", "******************** Question:\n", "<PERSON> is collecting herbs to make a poultice for her grandmother. She needs twice as many basil leaves as sage leaves and 5 fewer sage leaves than verbena leaves. If she needs 12 basil leaves, how many leaves total does she need? \n", "Answer:\n", "29 \n", "Response:\n", "<start_working_out>\n", "Let $b$ be the number of basil leaves, $s$ be the number of sage leaves, and $v$ be the number of verbena leaves.\n", "We are given that <PERSON> needs twice as many basil leaves as sage leaves, so $b = 2s$.\n", "We are also given that she needs 5 fewer sage leaves than verbena leaves, so $s = v - 5$.\n", "We are told that she needs 12 basil leaves, so $b = 12$.\n", "We can use the equation $b = 2s$ to solve for $s$:\n", "$12 = 2s$\n", "$s = \\frac{12}{2}$\n", "$s = 6$\n", "Now we can use the equation $s = v - 5$ to solve for $v$:\n", "$6 = v - 5$\n", "$v = 6 + 5$\n", "$v = 11$\n", "The total number of leaves needed is $b + s + v = 12 + 6 + 11$.\n", "$12 + 6 + 11 = 18 + 11 = 29$\n", "Therefore, the total number of leaves <PERSON> needs is 29.\n", "\n", "<SOLUTION>29 \n", "Extracted:\n", "29\n", "******************** Question:\n", "Over the past five years, on July 4th, the high temperature for Washington, DC has been: 90 degrees in 2020, 90 degrees in 2019, 90 degrees in 2018, 79 degrees in 2017 and 71 degrees in 2016. What is the average temperature for July 4th in Washington, DC over the past 5 years? \n", "Answer:\n", "84 \n", "Response:\n", "<start_working_out>\n", "To find the average temperature, we need to sum the temperatures and divide by the number of years (5).\n", "\n", "The temperatures are: 90, 90, 90, 79, 71.\n", "\n", "Sum of temperatures = 90 + 90 + 90 + 79 + 71 = 410\n", "\n", "Number of years = 5\n", "\n", "Average temperature = 410 / 5 = 82\n", "\n", "<SOLUTION>82\n", " \n", "Extracted:\n", "82\n", "******************** Question:\n", "<PERSON> can finish reading 30 pages in 60 minutes. <PERSON> can read 27 pages in 60 minutes and <PERSON> can read 25 pages in 60 minutes. If they have been reading for 240 minutes now, how many pages have they finished reading in total? \n", "Answer:\n", "328 \n", "Response:\n", "<start_working_out>\n", "Let R be the number of pages <PERSON> finishes in 60 minutes.\n", "Let L be the number of pages <PERSON> finishes in 60 minutes.\n", "Let C be the number of pages <PERSON> finishes in 60 minutes.\n", "We are given that R = 30, L = 27, and C = 25.\n", "We are also given that they have been reading for 240 minutes.\n", "Rene finishes $\\frac{30}{60} = \\frac{1}{2}$ of a page per minute.\n", "Lulu finishes $\\frac{27}{60} = \\frac{9}{20}$ of a page per minute.\n", "Cherry finishes $\\frac{25}{60} = \\frac{5}{12}$ of a page per minute.\n", "The total number of pages they finish in 240 minutes is:\n", "$T = \\frac{1}{2}R + \\frac{9}{20}L + \\frac{5}{12}C$\n", "$T = \\frac{1}{2}(30) + \\frac{9}{20}(27) + \\frac{5}{12}(25)$\n", "$T = 15 + \\frac{243}{20} + \\frac{125}{12}$\n", "To add these terms, we need to find a common denominator for 20 and 12. The least common multiple of 20 and 12 is 120.\n", "$T = 15 + \\frac{243 \\cdot 6}{20 \\cdot 6} + \\frac{125 \\cdot 10}{12 \\cdot 10}$\n", "$T = 15 + \\frac{1458}{120} + \\frac{1250}{120}$\n", "$T = 15 + \\frac{1458 + 1250}{120}$\n", "$T = 15 + \\frac{2708}{120}$\n", "$T = 15 + \\frac{677}{30}$\n", "$T = 15 + 22.5666...$\n", "However, we are given that they have been reading for 240 minutes. We need to find the total number of pages finished.\n", "Let $x$ be the total number of pages they have finished.\n", "<PERSON> finishes $R = 30$ pages in 60 minutes.\n", "<PERSON> finishes $L = 27$ pages in 60 minutes.\n", "Cherry finishes $C = 25$ pages in 60 minutes.\n", "The total pages finished in 60 minutes is $30 + 27 + 25 = 82$ pages.\n", "They have been reading for 240 minutes.\n", "The rate at which they finish pages is:\n", "Rene: $\\frac{30}{60} = \\frac{1}{2}$ pages per minute\n", "Lulu: $\\frac{27}{60} = \\frac{9}{20}$ pages per minute\n", "Cherry: $\\frac{25}{60} = \\frac{5}{12}$ pages per minute\n", "Total pages finished in 240 minutes:\n", "$T = \\frac{1}{2}R + \\frac{9}{20}L + \\frac{5}{12}C = \\frac{1}{2}( \n", "Extracted:\n", "None\n", "******************** Question:\n", "<PERSON> rings the small bell 4 times more than 1/3 as often as the big bell. If he rings both of them a combined total of 52 times, how many times does he ring the big bell? \n", "Answer:\n", "36 \n", "Response:\n", "<start_working_out>\n", "Let $m$ be the number of times <PERSON> rings the small bell, and $b$ be the number of times he rings the big bell.\n", "We are given that <PERSON> rings the small bell 4 times more than 1/3 as often as the big bell. This can be written as\n", "$$m = \\frac{1}{3}b + 4$$\n", "We are also given that he rings both bells a combined total of 52 times. So,\n", "$$m + b = 52$$\n", "Now we have a system of two linear equations with two variables:\n", "$$m = \\frac{1}{3}b + 4$$\n", "$$m + b = 52$$\n", "Substitute the first equation into the second equation:\n", "$$\\left(\\frac{1}{3}b + 4\\right) + b = 52$$\n", "$$\\frac{1}{3}b + b = 52 - 4$$\n", "$$\\frac{4}{3}b = 48$$\n", "$$b = \\frac{3}{4} \\cdot 48$$\n", "$$b = 3 \\cdot 12$$\n", "$$b = 36$$\n", "Now, substitute $b = 36$ into the equation $m = \\frac{1}{3}b + 4$:\n", "$$m = \\frac{1}{3}(36) + 4$$\n", "$$m = 12 + 4$$\n", "$$m = 16$$\n", "So, <PERSON> rings the small bell 16 times and the big bell 36 times. We want to find the number of times he rings the big bell, which is $b$.\n", "The number of times the big bell is $b = 36$.\n", "\n", "<SOLUTION>\n", "The number of times <PERSON> rings the big bell is 36.\n", "Final Answer: The final answer is $\\boxed{36}$ \n", "Extracted:\n", "36.\n", "******************** Question:\n", "<PERSON> fills out the daily crossword puzzle in the newspaper every day. He uses up a pencil to fill out the puzzles every two weeks. On average, it takes him 1050 words to use up a pencil. How many words are in each crossword puzzle on average? \n", "Answer:\n", "75 \n", "Response:\n", "<start_working_out>\n", "Let $w$ be the number of words in each crossword puzzle on average.\n", "<PERSON> uses up a pencil to fill out the puzzles every two weeks.\n", "We are given that it takes him 1050 words to use up a pencil.\n", "Since he uses up a pencil every two weeks, the number of pencils he uses is $\\frac{2}{2} = 1$ pencil every two weeks.\n", "The total number of words used is $1 \\times w = w$ words.\n", "We are given that he uses up a pencil to fill out the puzzles every two weeks, and it takes him 1050 words to use up a pencil.\n", "Therefore, $w = 1050$.\n", "So, the number of words in each crossword puzzle on average is 1050.\n", "\n", "<SOLUTION>1050 \n", "Extracted:\n", "1050\n", "******************** Question:\n", "<PERSON> can make a batch of a dozen cookies using 2 pounds of flour.  He uses 4 bags of flour each weighing 5 pounds.  If <PERSON> eats 15 cookies how many cookies are left? \n", "Answer:\n", "105 \n", "Response:\n", "<start_working_out>\n", "Let $C$ be the number of cookies <PERSON> can make.\n", "<PERSON> can make a dozen cookies, which means he can make 12 cookies.\n", "He uses 2 pounds of flour to make a dozen cookies.\n", "He uses 4 bags of flour each weighing 5 pounds.\n", "So, the total weight of flour he uses is $4 \\times 5 = 20$ pounds.\n", "Since he makes a dozen cookies, the amount of flour he uses is 2 pounds.\n", "The total weight of flour he uses is 2 pounds.\n", "The number of cookies he can make is $\\frac{2}{5}$ dozen.\n", "A dozen is 12 cookies, so $\\frac{2}{5}$ dozen is $\\frac{2}{5} \\times 12 = \\frac{24}{5} = 4.8$ cookies.\n", "Since he can only make a whole number of cookies, we can assume he makes 4 cookies.\n", "He uses 2 pounds of flour.\n", "If he makes 4 cookies, he uses $\\frac{4}{12} \\times 2 = \\frac{1}{3} \\times 2 = \\frac{2}{3}$ pounds of flour.\n", "<PERSON> eats 15 cookies.\n", "The number of cookies remaining is $15 - 4 = 11$ cookies.\n", "However, we don't need to calculate the value of the fraction of flour used.\n", "Let $x$ be the number of cookies <PERSON> can make with 2 pounds of flour.\n", "$x = \\frac{2}{5} \\times 12 = \\frac{24}{5} = 4.8$ cookies.\n", "Since he can only make a whole number of cookies, we are given that <PERSON> can make a dozen cookies, which is 12 cookies.\n", "He uses 2 pounds of flour for a dozen cookies.\n", "He uses 4 bags of flour each weighing 5 pounds. So he uses $4 \\times 5 = 20$ pounds of flour.\n", "Since he makes a dozen cookies, he uses $\\frac{2}{12} \\times 20 = \\frac{1}{6} \\times 20 = \\frac{20}{6} = \\frac{10}{3} = 3.33...$ pounds of flour.\n", "<PERSON> eats 15 cookies.\n", "The number of cookies left is $12 - 15 = -3$, which is not possible.\n", "Let $n$ be the number of cookies <PERSON> can make. He uses 2 pounds of flour.\n", "He uses 4 bags of flour, each weighing 5 pounds. So he uses $4 \\times 5 = 20$ pounds of flour.\n", "We have $n = \\frac{2}{5} \\times 12 = \\frac{24}{5} = 4.8$ cookies. Since he can only make whole cookies, he makes 4 cookies.\n", "<PERSON> eats 15 cookies.\n", "The number of cookies remaining is $4 - 15 = -11$, which is not possible.\n", "Let $x$ be the number of cookies <PERSON> makes. He uses 2 pounds of flour.\n", "He uses 4 bags of flour, each weighing 5 pounds. So he uses $4 \\times 5 = 20$ pounds of flour.\n", "We are given that he makes a dozen cookies, so $x = 12$.\n", "The amount of flour used is 2 pounds.\n", "The number of cookies <PERSON> can make is $\\frac{ \n", "Extracted:\n", "None\n", "******************** Question:\n", "<PERSON> decides to build a tin house by collecting 500 tins in a week. On the first day, he collects 50 tins. On the second day, he manages to collect 3 times that number. On the third day, he collects 50 tins fewer than the number he collected on the second day. If he collects an equal number of tins on the remaining days of the week, what's the number of tins he collected each day for the rest of the week? \n", "Answer:\n", "50 \n", "Response:\n", "<start_working_out>\n", "Let $x$ be the number of tins <PERSON> collected on the third day.\n", "On the first day, he collected 50 tins.\n", "On the second day, he collected 3 times that number, so he collected $3 \\times 50 = 150$ tins.\n", "On the third day, he collected 50 tins fewer than the number he collected on the second day, so he collected $150 - 50 = 100$ tins.\n", "The total number of tins collected in the first three days is $50 + 150 + 100 = 300$ tins.\n", "He has 500 tins to collect in total, so the number of tins collected on the remaining days is $500 - 300 = 200$ tins.\n", "There are 7 days in a week. He collects an equal number of tins on the remaining 7 days.\n", "So, the number of tins collected on each of the remaining 7 days is $\\frac{200}{7} \\approx 28.57$. Since the number of tins must be an integer, we can't have a fraction. However, the problem states that he collects an equal number of tins on the remaining days of the week.\n", "\n", "Let $n$ be the number of tins collected on each of the remaining 7 days. Then $7n = 200$.\n", "$n = \\frac{200}{7} \\approx 28.57$. Since the number of tins collected must be an integer, we need to re-evaluate.\n", "\n", "Let $x$ be the number of tins collected on the first day.\n", "Let $y$ be the number of tins collected on the second day.\n", "Let $z$ be the number of tins collected on the third day.\n", "Let $w$ be the number of tins collected on the fourth day.\n", "Let $v$ be the number of tins collected on the fifth day.\n", "Let $u$ be the number of tins collected on the sixth day.\n", "Let $t$ be the number of tins collected on the seventh day.\n", "\n", "We are given:\n", "$x = 50$\n", "$y = 3x = 3(50) = 150$\n", "$z = y - 50 = 150 - 50 = 100$\n", "$w = x - 50 = 50 - 50 = 0$\n", "$v = w + z = 0 + 100 = 100$\n", "$u = v + t = 100 + t$\n", "$t = 500 - (x + y + z + w + v + u) = 500 - (50 + 150 + 100 + 0 + 100 + (100+t)) = 500 - (50 + 150 + 100 + 0 + 100 + 100 + t) = 500 - (400 + 300 + t) = 500 - (700 + t) = -200 - t$\n", "Since $t$ must be non-negative, we have $-200 - t \\geq 0$, so $t \\leq -200$. This is not possible.\n", "\n", "Let's re-interpret the problem.\n", "<PERSON> collects 50 tins on \n", "Extracted:\n", "None\n", "******************** Question:\n", "A jar of jellybeans has 14 blue jellybeans, 26 purple jellybeans and 40 orange jellybeans. If there are 200 jellybeans in the jar, how many are there of the red color? \n", "Answer:\n", "120 \n", "Response:\n", "<start_working_out>\n", "Let $B$ be the number of blue jellybeans, $P$ be the number of purple jellybeans, and $O$ be the number of orange jellybeans.\n", "We are given that $B = 14$, $P = 26$, and $O = 40$.\n", "The total number of jellybeans is $B + P + O = 14 + 26 + 40 = 80$.\n", "We are told that there are 200 jellybeans in the jar.\n", "Let $R$ be the number of red jellybeans.\n", "The total number of jellybeans is the sum of the number of blue, purple, orange, and red jellybeans.\n", "So, $B + P + O + R = 200$.\n", "Substituting the given values, we have $14 + 26 + 40 + R = 200$.\n", "$80 + R = 200$.\n", "$R = 200 - 80$.\n", "$R = 120$.\n", "Therefore, there are 120 red jellybeans in the jar.\n", "\n", "<SOLUTION>120 \n", "Extracted:\n", "120\n", "******************** Question:\n", "Five adults and two children go to see a movie and buy $12 worth of concessions. The total cost of their trip is $76. If each child's ticket is $7, how much, in dollars, are the adult tickets? \n", "Answer:\n", "10 \n", "Response:\n", "<start_working_out>\n", "Let $a$ be the number of adult tickets and $c$ be the number of children's tickets.\n", "We are given that there are five adults and two children, so $a+c = 5+2 = 7$.\n", "We are also given that the total cost of the trip is $12 worth of concessions, and the total cost is $76. So, $7a + 7c = 76$.\n", "We have a system of two linear equations with two variables:\n", "\\begin{align*} \\label{eq:1} a+c &= 7 \\\\ 7a+7c &= 76\\end{align*}\n", "From the first equation, we can write $c = 7-a$. Substitute this into the second equation:\n", "$$7a + 7(7-a) = 76$$\n", "$$7a + 49 - 7a = 76$$\n", "$$49 = 76$$\n", "This equation is inconsistent, so we must have made a mistake.\n", "Let's try another approach. Let $x$ be the price of an adult ticket and $y$ be the price of a child's ticket.\n", "We are given that there are five adults and two children, so $a$ is the number of adult tickets and $c$ is the number of children's tickets.\n", "We are given that there are 5 adults and 2 children, so $a+c = 5+2 = 7$.\n", "We are given that the total cost is $12 worth of concessions.\n", "The cost of the adult tickets is $ax$ and the cost of the children's tickets is $cy$.\n", "The total cost is $ax + cy = 12$.\n", "We are given that each child's ticket is $7, so $y = 7$.\n", "Thus, $ax + 7c = 12$.\n", "Since $a+c = 7$, we can write $c = 7-a$. Substitute this into the equation:\n", "$$ax + 7(7-a) = 12$$\n", "$$ax + 49 - 7a = 12$$\n", "$$ax - 7a = 12 - 49$$\n", "$$a(x-7) = -37$$\n", "$$a = \\frac{-37}{x-7}$$\n", "Since $a$ and $c$ must be integers, $x-7$ must be a factor of 37. Since 37 is a prime number, $x-7$ can be $1$ or $37$.\n", "If $x-7 = 1$, then $x = 8$. Then $a = \\frac{-37}{1} = -37$, which is not possible.\n", "If $x-7 = 37$, then $x = 44$. Then $a = \\frac{-37}{37} = -1$, which is not possible.\n", "Let's try a different approach.\n", "The total cost of the trip is $12. The number of adults is $a$ and the number of children is $c$.\n", "$$ax + 7c = 12$$\n", "We are given that there are 5 adults and 2 children, so $a+c = 5+2 = 7$.\n", "Substituting $c = 7-a$ into the equation, we get\n", "$$ax + 7(7-a) = 12$$\n", "$$ax + 49 \n", "Extracted:\n", "None\n", "******************** Question:\n", "<PERSON><PERSON> had 26 green marbles. Then she bought 6 bags of blue marbles. There were 10 marbles in each bag.  She created a gift of 6 green marbles and 8 blue marbles and gave it to a friend. How many marbles does <PERSON><PERSON> have now? \n", "Answer:\n", "72 \n", "Response:\n", "<start_working_out>\n", "<PERSON><PERSON> starts with 26 green marbles.\n", "She buys 6 bags of blue marbles, with 10 marbles in each bag. So she buys 6 * 10 = 60 marbles of blue.\n", "Now she has 26 + 60 = 86 marbles.\n", "She creates a gift of 6 green marbles and 8 blue marbles.\n", "So she has 6 green marbles + 8 blue marbles = 14 marbles.\n", "After creating the gift, she has 86 - 14 = 72 marbles.\n", "<end_working_out>\n", "<PERSON><PERSON> has 72 marbles now.\n", " \n", "Extracted:\n", "None\n"]}], "source": ["trainer = GRPOTrainer(\n", "    model = model,\n", "    processing_class = tokenizer,\n", "    reward_funcs = [\n", "        match_format_exactly,\n", "        match_format_approximately,\n", "        check_answer,\n", "        check_numbers,\n", "    ],\n", "    args = training_args,\n", "    train_dataset = dataset,\n", ")\n", "trainer.train()"]}, {"cell_type": "markdown", "metadata": {"id": "tlaUdxC_VHpz"}, "source": ["<a name=\"Inference\"></a>\n", "### Inference\n", "Now let's try the model we just trained!"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "qtcz_lpbVC92", "outputId": "bc1e6dbc-4e4e-4382-f7aa-1f56e716f044"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<start_working_out>\n", "The square root of 101 is approximately 10.0498756.\n", "<SOLUTION>\n", "10.0498756<end_of_turn>\n"]}], "source": ["messages = [\n", "    {\"role\": \"system\", \"content\": system_prompt},\n", "    {\"role\": \"user\",   \"content\": \"What is the sqrt of 101?\"},\n", "]\n", "\n", "text = tokenizer.apply_chat_template(\n", "    messages,\n", "    add_generation_prompt = True, # Must add for generation\n", "    tokenize = False,\n", ")\n", "from transformers import TextStreamer\n", "_ = model.generate(\n", "    **tokenizer(text, return_tensors = \"pt\").to(\"cuda\"),\n", "    max_new_tokens = 64, # Increase for longer outputs!\n", "    # Recommended Gemma-3 settings!\n", "    temperature = 1.0, top_p = 0.95, top_k = 64,\n", "    streamer = TextStreamer(tokenizer, skip_prompt = True),\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "-NUEmHFSYNTp"}, "source": ["<a name=\"Save\"></a>\n", "### Saving, loading finetuned models\n", "To save the final model as LoRA adapters, either use Huggingface's `push_to_hub` for an online save or `save_pretrained` for a local save.\n", "\n", "**[NOTE]** This ONLY saves the LoRA adapters, and not the full model. To save to 16bit or GGUF, scroll down!"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "NjXGTkp7YNtB", "outputId": "7dbe3643-d756-4c32-acaf-2d5fe76f52ef"}, "outputs": [{"data": {"text/plain": ["('gemma-3/tokenizer_config.json',\n", " 'gemma-3/special_tokens_map.json',\n", " 'gemma-3/tokenizer.model',\n", " 'gemma-3/added_tokens.json',\n", " 'gemma-3/tokenizer.json')"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["model.save_pretrained(\"gemma-3\")  # Local saving\n", "tokenizer.save_pretrained(\"gemma-3\")\n", "# model.push_to_hub(\"HF_ACCOUNT/gemma-3\", token = \"...\") # Online saving\n", "# tokenizer.push_to_hub(\"HF_ACCOUNT/gemma-3\", token = \"...\") # Online saving"]}, {"cell_type": "markdown", "metadata": {"id": "52WMb3k_YPt8"}, "source": ["### Saving to float16 for VLLM\n", "\n", "We also support saving to `float16` directly for deployment! We save it in the folder `gemma-3-finetune`. Set `if False` to `if True` to let it run!"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"id": "QyEjW-WuYQIm"}, "outputs": [], "source": ["if False: # Change to True to save finetune!\n", "    model.save_pretrained_merged(\"gemma-3-finetune\", tokenizer)"]}, {"cell_type": "markdown", "metadata": {"id": "NRrqfyaRaXRL"}, "source": ["If you want to upload / push to your Hugging Face account, set `if False` to `if True` and add your Hugging Face token and upload location!"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"id": "_-B07m_HC5i7"}, "outputs": [], "source": ["if False: # Change to True to upload finetune\n", "    model.push_to_hub_merged(\n", "        \"HF_ACCOUNT/gemma-3-finetune\", tokenizer,\n", "        token = \"hf_...\"\n", "    )"]}, {"cell_type": "markdown", "metadata": {"id": "5JMDDS0bC7jT"}, "source": ["### GGUF / llama.cpp Conversion\n", "To save to `GGUF` / `llama.cpp`, we support it natively now for all models! For now, you can convert easily to `Q8_0, F16 or BF16` precision. `Q4_K_M` for 4bit will come later!"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"id": "aX3xMj8hC6e0"}, "outputs": [], "source": ["if False: # Change to True to save to GGUF\n", "    model.save_pretrained_gguf(\n", "        \"gemma-3-finetune\",\n", "        quantization_type = \"Q8_0\", # For now only Q8_0, BF16, F16 supported\n", "    )"]}, {"cell_type": "markdown", "metadata": {"id": "0CfqZW5rC9zv"}, "source": ["Likewise, if you want to instead push to GGUF to your Hugging Face account, set `if False` to `if True` and add your Hugging Face token and upload location!"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"id": "SG6-dP0JC-G2"}, "outputs": [], "source": ["if False: # Change to True to upload GGUF\n", "    model.push_to_hub_gguf(\n", "        \"gemma-3-finetune\",\n", "        quantization_type = \"Q8_0\", # Only Q8_0, BF16, F16 supported\n", "        repo_id = \"HF_ACCOUNT/gemma-finetune-gguf\",\n", "        token = \"hf_...\",\n", "    )"]}, {"cell_type": "markdown", "metadata": {"id": "-F2GVrPrDArH"}, "source": ["Now, use the `gemma-3-finetune.gguf` file or `gemma-3-finetune-Q4_K_M.gguf` file in llama.cpp or a UI based system like Jan or Open WebUI. You can install Jan [here](https://github.com/janhq/jan) and Open WebUI [here](https://github.com/open-webui/open-webui)\n", "\n", "And we're done! If you have any questions on Unsloth, we have a [Discord](https://discord.gg/unsloth) channel! If you find any bugs or want to keep updated with the latest LLM stuff, or need help, join projects etc, feel free to join our Discord!\n", "\n", "Some other links:\n", "1. Train your own reasoning model - Llama GRPO notebook [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.1_(8B)-GRPO.ipynb)\n", "2. Saving finetunes to Ollama. [Free notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb)\n", "3. Llama 3.2 Vision finetuning - Radiography use case. [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.2_(11B)-Vision.ipynb)\n", "6. See notebooks for DPO, ORPO, Continued pretraining, conversational finetuning and more on our [documentation](https://docs.unsloth.ai/get-started/unsloth-notebooks)!\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://unsloth.ai\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "  <a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord.png\" width=\"145\"></a>\n", "  <a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a>\n", "\n", "  Join Discord if you need help + ⭐️ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐️\n", "</div>\n"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "include_colab_link": true, "provenance": []}, "kernelspec": {"display_name": "unsloth_env", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.11.11"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"013fb3c914e944b59b24075bbb6d70cf": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "028216cd27cb4d23880b8577d2234574": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "02d6b8e9a02b470a92f700d9e7fea5d4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "04505a0929fd448fb634f5d2517d0143": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7c4cd321445b43bfa818b599d42c8cb7", "IPY_MODEL_50503005b58845b79254f89f95fb03d9", "IPY_MODEL_9da829640aab4d69a8753118e2a42014"], "layout": "IPY_MODEL_a4013e1c447446f7b45bed913b15351d"}}, "099719146cf54e7f828c53605325b3f3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cac3cce4717d4b10ae9f6a12468e6528", "max": 419088, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_62370afab24e49e0b89b34d0907414c8", "value": 419088}}, "0af4f1ee49f746769e108abc15c2df33": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_0e90bdc70e8a42b7993c4c5589677040", "IPY_MODEL_625fc21ae1da4e3a998683d62b0a945e", "IPY_MODEL_80c8d8e625fb454881e8c12e2b5725dc"], "layout": "IPY_MODEL_67f0b8762670467d85d48f99405c8b1f"}}, "0c96fa0b7ed344f4be44632451b406d7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0e90bdc70e8a42b7993c4c5589677040": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_551b94fe4b3c4a4f8f3220d013a6d897", "placeholder": "​", "style": "IPY_MODEL_29d36346bc75470eacd30aebf1423e14", "value": "tokenizer.json: 100%"}}, "0ef32700424c4799b8216de4ed8bbbb9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "10c20527dc19466eb4d6eb325529a0df": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "116d96200f7d4be5b2c1cd32d9be9ec2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a7142d54e9d54e94af5cac45f82826fb", "max": 2306545, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2de092a4e86748fd882ca2bb5cf03c94", "value": 2306545}}, "11f4e6814d4c4fdfae4e629b3d36629a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_383d2f04eccd4e38ad791494a06423ed", "placeholder": "​", "style": "IPY_MODEL_36cabdee7cd645a58e1ffc794674a322", "value": "special_tokens_map.json: 100%"}}, "1664b23faa1b4292bb5727bd525c45be": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_5017bf753b93420f94bf27eeded7ca79", "IPY_MODEL_099719146cf54e7f828c53605325b3f3", "IPY_MODEL_db5911df3b4e4691ad183c5a90aee102"], "layout": "IPY_MODEL_a90a59dd2fa844fcb0a4cadfeeb3b702"}}, "185b378797334c819f8a199760cac945": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "185b6b8280944e1ab7503c305f3d8313": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_772360d1551141c5ab0a877ae4ed1c76", "placeholder": "​", "style": "IPY_MODEL_b0c51c819fff44c5a6e8e626fec9e937", "value": "tokenizer_config.json: 100%"}}, "19731cf654e64eb3905f02f4ae277e8c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "danger", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_013fb3c914e944b59b24075bbb6d70cf", "max": 1999811208, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_859b75a69281413383fc1c0946bf63d0", "value": 1999811018}}, "1ba6d4a3394e464da1c8cf0f94ca9bce": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1da84b32592d432fb1a57358ddcdceff": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1f47a0e576414befbf672cd20ac44fa8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_5e4bcd42eb8a4247ac7cc67034b3f415", "IPY_MODEL_ed53e921682348b28e2be40eaf96cbf7", "IPY_MODEL_547512d47e0f43d7a81bb817a3b98b59"], "layout": "IPY_MODEL_ad09ac69cad24880a0515c1370011c36"}}, "21366789f69d431bad2ac2c8d1ec1565": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "21f2aec6d7af4c3a82306ad235674829": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "248a7ac8ad0a4c9c9638502676bf2a91": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c8c4788a0e6a42daa111a96352d245fe", "max": 1157007, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_028216cd27cb4d23880b8577d2234574", "value": 1157007}}, "25c7874a020344e7aad2869f7a27db4e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "29a503c15a534923ad510d1c4e88c064": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "29d36346bc75470eacd30aebf1423e14": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2de092a4e86748fd882ca2bb5cf03c94": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "31c11ed4511b45cd96d2f5580f550113": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6decae16dd5b404ba272d89df9c1372b", "placeholder": "​", "style": "IPY_MODEL_9151ae8e05634cf4a8ce42677f211bac", "value": "Map: 100%"}}, "340c07f1b7b14527a378880f9166ef55": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "366ae469442b4e3f93d9b074074d69f6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "36cabdee7cd645a58e1ffc794674a322": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "383d2f04eccd4e38ad791494a06423ed": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "38db654dd1674055b98958856c87d698": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_11f4e6814d4c4fdfae4e629b3d36629a", "IPY_MODEL_f73516b2403f411d925618cfa2af5f46", "IPY_MODEL_d767074c80254322b3062a6c6fe2cd5b"], "layout": "IPY_MODEL_29a503c15a534923ad510d1c4e88c064"}}, "393e1a3fd0e24c94b856b6d806012d52": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "3f609c16bff04203884e64bde39bac63": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_671b4725f18a4c809b3c97a65ec9e405", "placeholder": "​", "style": "IPY_MODEL_faa61469d4bc46aebe74d5401d42c3ab", "value": " 35.0/35.0 [00:00&lt;00:00, 2.66kB/s]"}}, "3fabb349d1f943b09ed25784a0d0ab0a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_31c11ed4511b45cd96d2f5580f550113", "IPY_MODEL_8f85cea6a8f2402ca1c49b4c6da61a74", "IPY_MODEL_f6a520e1570d4f7faffc1b6e2c6200d2"], "layout": "IPY_MODEL_f1d996d267de44c1973df32c354bee97"}}, "3fff97801bec4e0fa9fddf5824ae3de5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_851cbec6dad64cc3b1f0a6bec57af9bf", "IPY_MODEL_c83661398fb44a18aa52c6280c3f4b25", "IPY_MODEL_a90ad0da622b401d877873412119e4e0"], "layout": "IPY_MODEL_f19ac2f749e140b09f5cbf3c4663ef3a"}}, "444aa81458bf4790915c188069506864": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "45aa2714bf0b44e9af7668c674f63863": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "45eda31f22294728bbca360abad799e6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4699d5847ab34d90a637b5025810e359": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7a55b04eb2d34f36a4ca140ae90c0d96", "IPY_MODEL_116d96200f7d4be5b2c1cd32d9be9ec2", "IPY_MODEL_8b48641e1f41441b8390f5b4f0e3be9b"], "layout": "IPY_MODEL_9c9123b394c34e82b56f0589389703b4"}}, "4c2ccce4364c4ea1b682c378950f964d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4cabdb178b97464e875d507dd1410bbf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4f313311e3ee4e949580ab8a808561a0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5017bf753b93420f94bf27eeded7ca79": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cf83a6e558f64ff98276981a82f3b2ac", "placeholder": "​", "style": "IPY_MODEL_0ef32700424c4799b8216de4ed8bbbb9", "value": "test-00000-of-00001.parquet: 100%"}}, "50503005b58845b79254f89f95fb03d9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8a21cb24786049e2946c9a050a6673ab", "max": 7940, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_340c07f1b7b14527a378880f9166ef55", "value": 7940}}, "547512d47e0f43d7a81bb817a3b98b59": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d1e38bbf7593462bb87b14bacee9e3d9", "placeholder": "​", "style": "IPY_MODEL_846c1dd9b3214c68bbfc362166a6b9ee", "value": " 7473/7473 [00:00&lt;00:00, 5203.68 examples/s]"}}, "54c603df4e174b70bd03bce59de287b2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "551b94fe4b3c4a4f8f3220d013a6d897": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5686cb44652d4afda84ea8283d232c99": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_964fcc9cfd8a4e98a13b259e8253fbb3", "max": 1319, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_6306f01e5e14455f96f1d852ac3b323f", "value": 1319}}, "5e4bcd42eb8a4247ac7cc67034b3f415": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9677dc2a2f4847d89d3ca42c01435205", "placeholder": "​", "style": "IPY_MODEL_645b707de96a4c2eb07eb118db311fb6", "value": "Generating train split: 100%"}}, "61d7ca5d56f14d7d93d5cf5e5b712da2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c3e88f16997444c490b4950fe5b34e0b", "max": 215, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_54c603df4e174b70bd03bce59de287b2", "value": 215}}, "6216e6cebe7b4f6caf4b7a02a3899cf3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_185b6b8280944e1ab7503c305f3d8313", "IPY_MODEL_248a7ac8ad0a4c9c9638502676bf2a91", "IPY_MODEL_ef65703ac8e94c3482dac54b124e8c93"], "layout": "IPY_MODEL_4c2ccce4364c4ea1b682c378950f964d"}}, "62370afab24e49e0b89b34d0907414c8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "625fc21ae1da4e3a998683d62b0a945e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_901262ba6fd24d6f90f283301218e6aa", "max": 33384568, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f029d16fa30a49f098261346df37af1e", "value": 33384568}}, "6306f01e5e14455f96f1d852ac3b323f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "645b707de96a4c2eb07eb118db311fb6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "669cb00554134e8287d0daae7372d397": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "671b4725f18a4c809b3c97a65ec9e405": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "67eda18557dc461483821ffd44a25eb4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "67f0b8762670467d85d48f99405c8b1f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6ca6f0f3e43440928ed55839c1780dd9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "6d1b19657db94907b6fdf0a3b3a05e84": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6decae16dd5b404ba272d89df9c1372b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "70a0b21c05e642e5891bb8b91cfb2217": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "772360d1551141c5ab0a877ae4ed1c76": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "79980e3b83ce4f35a7858cc8b12c2c2a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7a55b04eb2d34f36a4ca140ae90c0d96": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_669cb00554134e8287d0daae7372d397", "placeholder": "​", "style": "IPY_MODEL_9e7bb56731134d9cba89cbde208358e0", "value": "train-00000-of-00001.parquet: 100%"}}, "7c4cd321445b43bfa818b599d42c8cb7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cec948b41ee348fcbdf2f5fa290ffe42", "placeholder": "​", "style": "IPY_MODEL_6d1b19657db94907b6fdf0a3b3a05e84", "value": "README.md: 100%"}}, "7d4b49f0c54046a89039d25ee7c11f6f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7ed0c86e90ca4146a034034e3c295d25": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8029855dfa614963a7f5ff11d48dcbdb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "80c8d8e625fb454881e8c12e2b5725dc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1da84b32592d432fb1a57358ddcdceff", "placeholder": "​", "style": "IPY_MODEL_bbcd20b75ce445bea7be9597efa68d73", "value": " 33.4M/33.4M [00:00&lt;00:00, 144MB/s]"}}, "8464d2f310b045808bcd7206aa7c8cc5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "846c1dd9b3214c68bbfc362166a6b9ee": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "851cbec6dad64cc3b1f0a6bec57af9bf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_70a0b21c05e642e5891bb8b91cfb2217", "placeholder": "​", "style": "IPY_MODEL_444aa81458bf4790915c188069506864", "value": "tokenizer.model: 100%"}}, "859b75a69281413383fc1c0946bf63d0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "86e9660b2d72460c836d9bba348be56b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "88e52f4b8d3a43bab15b441fdf768202": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_02d6b8e9a02b470a92f700d9e7fea5d4", "placeholder": "​", "style": "IPY_MODEL_8464d2f310b045808bcd7206aa7c8cc5", "value": "Generating test split: 100%"}}, "8a21cb24786049e2946c9a050a6673ab": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8b48641e1f41441b8390f5b4f0e3be9b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0c96fa0b7ed344f4be44632451b406d7", "placeholder": "​", "style": "IPY_MODEL_bf96f070c2f54468a674f11681fcb22d", "value": " 2.31M/2.31M [00:00&lt;00:00, 17.7MB/s]"}}, "8ca7d620dc5d415a83b50f134317d925": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8d86ec6ac8ea4e0dac38ad19655c2897": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ee082e1dadf74b589840ef533e4d0637", "IPY_MODEL_61d7ca5d56f14d7d93d5cf5e5b712da2", "IPY_MODEL_aea85dac6725482ebac56f11c8e57e34"], "layout": "IPY_MODEL_bf3141f5d0ae44ecb99707caeb2aaecd"}}, "8f2974213b954318902d39a1fda9b3fb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8f85cea6a8f2402ca1c49b4c6da61a74": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ba4ac2596dda42698ae048f9b7a11c61", "max": 7473, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_393e1a3fd0e24c94b856b6d806012d52", "value": 7473}}, "901262ba6fd24d6f90f283301218e6aa": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9151ae8e05634cf4a8ce42677f211bac": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "964fcc9cfd8a4e98a13b259e8253fbb3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9677dc2a2f4847d89d3ca42c01435205": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "977b794c5d1b4910a2126193ee27f850": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "983908452fb1423a8b85f99b58a7cb1f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b67486cb78274f2baa4f9afdc2fd7e3c", "IPY_MODEL_19731cf654e64eb3905f02f4ae277e8c", "IPY_MODEL_b62bf844938f4591a610f6c4ec836464"], "layout": "IPY_MODEL_7ed0c86e90ca4146a034034e3c295d25"}}, "9c9123b394c34e82b56f0589389703b4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9da829640aab4d69a8753118e2a42014": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_67eda18557dc461483821ffd44a25eb4", "placeholder": "​", "style": "IPY_MODEL_4cabdb178b97464e875d507dd1410bbf", "value": " 7.94k/7.94k [00:00&lt;00:00, 179kB/s]"}}, "9e7bb56731134d9cba89cbde208358e0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a1fced067e1e40cbb08e25c9377123e0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a4013e1c447446f7b45bed913b15351d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a6030d7b5bbd460fb5aa1356c607ec82": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a7142d54e9d54e94af5cac45f82826fb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a90a59dd2fa844fcb0a4cadfeeb3b702": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a90ad0da622b401d877873412119e4e0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_def9dbef26334436a6b2298b5b153f47", "placeholder": "​", "style": "IPY_MODEL_eea838fbfd2c4e82b1769fde1036487e", "value": " 4.69M/4.69M [00:00&lt;00:00, 26.2MB/s]"}}, "a9dbcb0e164544ba8321a7916500009d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "aaf739645f654cd6a94d897358b2c2e0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ad09ac69cad24880a0515c1370011c36": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "aea85dac6725482ebac56f11c8e57e34": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_45aa2714bf0b44e9af7668c674f63863", "placeholder": "​", "style": "IPY_MODEL_8ca7d620dc5d415a83b50f134317d925", "value": " 215/215 [00:00&lt;00:00, 13.4kB/s]"}}, "b0c51c819fff44c5a6e8e626fec9e937": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b62bf844938f4591a610f6c4ec836464": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d9e72108a08b447ea2a29932391fe429", "placeholder": "​", "style": "IPY_MODEL_21366789f69d431bad2ac2c8d1ec1565", "value": " 2.00G/2.00G [00:15&lt;00:00, 71.2MB/s]"}}, "b67486cb78274f2baa4f9afdc2fd7e3c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_21f2aec6d7af4c3a82306ad235674829", "placeholder": "​", "style": "IPY_MODEL_fd6ff32a3aa7479e8b5d0cda47819742", "value": "model.safetensors: 100%"}}, "ba4ac2596dda42698ae048f9b7a11c61": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bbcd20b75ce445bea7be9597efa68d73": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "bf3141f5d0ae44ecb99707caeb2aaecd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bf96f070c2f54468a674f11681fcb22d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c1401835d0a14d5b8482b92fafc64d49": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_88e52f4b8d3a43bab15b441fdf768202", "IPY_MODEL_5686cb44652d4afda84ea8283d232c99", "IPY_MODEL_ecdd7380c74a4cdaaea3d7c94285c5cf"], "layout": "IPY_MODEL_977b794c5d1b4910a2126193ee27f850"}}, "c3e88f16997444c490b4950fe5b34e0b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c6de3f102e1d4a378d3c2b1c46b07f31": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c83661398fb44a18aa52c6280c3f4b25": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7d4b49f0c54046a89039d25ee7c11f6f", "max": 4689074, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_79980e3b83ce4f35a7858cc8b12c2c2a", "value": 4689074}}, "c8c4788a0e6a42daa111a96352d245fe": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cac3cce4717d4b10ae9f6a12468e6528": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cec948b41ee348fcbdf2f5fa290ffe42": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cf83a6e558f64ff98276981a82f3b2ac": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d1e38bbf7593462bb87b14bacee9e3d9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d6bdbc8fc2ee47ab9e01c3f8dff694d4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d767074c80254322b3062a6c6fe2cd5b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8029855dfa614963a7f5ff11d48dcbdb", "placeholder": "​", "style": "IPY_MODEL_e74fcfb683074f27806b6aaae329b74d", "value": " 670/670 [00:00&lt;00:00, 74.3kB/s]"}}, "d7b48dafe15947c9b225681c4a326581": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d9902e3bece446928598402ed953585f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_f43ad8be46034c2e9fed4cf23ea6d103", "IPY_MODEL_efaa563da24149aaaa153b7e8c473394", "IPY_MODEL_3f609c16bff04203884e64bde39bac63"], "layout": "IPY_MODEL_4f313311e3ee4e949580ab8a808561a0"}}, "d9e72108a08b447ea2a29932391fe429": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "db5911df3b4e4691ad183c5a90aee102": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a9dbcb0e164544ba8321a7916500009d", "placeholder": "​", "style": "IPY_MODEL_a6030d7b5bbd460fb5aa1356c607ec82", "value": " 419k/419k [00:00&lt;00:00, 9.62MB/s]"}}, "def9dbef26334436a6b2298b5b153f47": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e641631954a14fcfbba1221ba801d53a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e74fcfb683074f27806b6aaae329b74d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ecdd7380c74a4cdaaea3d7c94285c5cf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_25c7874a020344e7aad2869f7a27db4e", "placeholder": "​", "style": "IPY_MODEL_86e9660b2d72460c836d9bba348be56b", "value": " 1319/1319 [00:00&lt;00:00, 12220.76 examples/s]"}}, "ed53e921682348b28e2be40eaf96cbf7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d6bdbc8fc2ee47ab9e01c3f8dff694d4", "max": 7473, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_c6de3f102e1d4a378d3c2b1c46b07f31", "value": 7473}}, "ee082e1dadf74b589840ef533e4d0637": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_185b378797334c819f8a199760cac945", "placeholder": "​", "style": "IPY_MODEL_a1fced067e1e40cbb08e25c9377123e0", "value": "generation_config.json: 100%"}}, "eea838fbfd2c4e82b1769fde1036487e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ef65703ac8e94c3482dac54b124e8c93": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_45eda31f22294728bbca360abad799e6", "placeholder": "​", "style": "IPY_MODEL_d7b48dafe15947c9b225681c4a326581", "value": " 1.16M/1.16M [00:00&lt;00:00, 6.41MB/s]"}}, "efaa563da24149aaaa153b7e8c473394": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1ba6d4a3394e464da1c8cf0f94ca9bce", "max": 35, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_6ca6f0f3e43440928ed55839c1780dd9", "value": 35}}, "f029d16fa30a49f098261346df37af1e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f0c1afb62d8a4616a8df27c9de866916": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f19ac2f749e140b09f5cbf3c4663ef3a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f1d996d267de44c1973df32c354bee97": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f43ad8be46034c2e9fed4cf23ea6d103": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_aaf739645f654cd6a94d897358b2c2e0", "placeholder": "​", "style": "IPY_MODEL_f0c1afb62d8a4616a8df27c9de866916", "value": "added_tokens.json: 100%"}}, "f6a520e1570d4f7faffc1b6e2c6200d2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_10c20527dc19466eb4d6eb325529a0df", "placeholder": "​", "style": "IPY_MODEL_8f2974213b954318902d39a1fda9b3fb", "value": " 7473/7473 [00:00&lt;00:00, 9263.20 examples/s]"}}, "f73516b2403f411d925618cfa2af5f46": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e641631954a14fcfbba1221ba801d53a", "max": 670, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_366ae469442b4e3f93d9b074074d69f6", "value": 670}}, "faa61469d4bc46aebe74d5401d42c3ab": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "fd6ff32a3aa7479e8b5d0cda47819742": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "state": {}}}}, "nbformat": 4, "nbformat_minor": 0}