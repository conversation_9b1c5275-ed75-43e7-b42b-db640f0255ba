{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["To run this, press \"*Runtime*\" and press \"*Run all*\" on a **free** Tesla T4 Google Colab instance!\n", "<div class=\"align-center\">\n", "<a href=\"https://unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "<a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord button.png\" width=\"145\"></a>\n", "<a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a></a> Join Disco<PERSON> if you need help + ⭐ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐\n", "</div>\n", "\n", "To install Unsloth on your own computer, follow the installation instructions on our Github page [here](https://docs.unsloth.ai/get-started/installing-+-updating).\n", "\n", "You will learn how to do [data prep](#Data), how to [train](#Train), how to [run the model](#Inference), & [how to save it](#Save)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### News"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Read our **[TTS Guide](https://docs.unsloth.ai/basics/text-to-speech-tts-fine-tuning)** for instructions and all our notebooks.\n", "\n", "Read our **[Qwen3 Guide](https://docs.unsloth.ai/basics/qwen3-how-to-run-and-fine-tune)** and check out our new **[Dynamic 2.0](https://docs.unsloth.ai/basics/unsloth-dynamic-2.0-ggufs)** quants which outperforms other quantization methods!\n", "\n", "Visit our docs for all our [model uploads](https://docs.unsloth.ai/get-started/all-our-models) and [notebooks](https://docs.unsloth.ai/get-started/unsloth-notebooks).\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": "%%capture\n!pip install pip3-autoremove\n!pip install torch torchvision torchaudio xformers --index-url https://download.pytorch.org/whl/cu124\n!pip install unsloth\n# !pip install --upgrade transformers==4.52.3"}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 511, "referenced_widgets": ["98d2cd94be9c454f83a18c8bf5fe9aec", "c6d906d5fa284667a7021b56fb731e98", "2394d94e0e714ffdb528b55605f40dc0", "144522321d134e94858678d525a85f80", "eaa4f5b890c3436bb8da3e74a8eeb237", "a85bb0dcd185406c885af8d491cf3c22", "91a165c1dade4c1b9d7c01771b61291e", "24f8f69538bb469192088f3ffa1ed624", "f5123643c11240c0bcc3d3588e8e8a1b", "ef9a483fb2a3478cb94331868ab2db90", "ce61267651164b50aa49876a05b7296a", "289cd006e4ff4f018e758890e148c852", "c8abe988d9864ca1955430b023c223ad", "ad5361da5e774447b07bab7e0f272813", "7fd8253214984e578754c1a7f184687e", "e84d7f0735e045f498a85deee72e4a36", "0d6664235d744c66bc567215699966b3", "14ec87fd00454cc9b6e75a37d9cf2c1c", "79e970816ead4141a8dc3c534db3a54c", "f90c77c1bd02487da4d91289243c6a28", "b0ea1c891cd44fb38f874d28f1b71ad7", "017cce265bf84be7bd9b8ebe5f25047e", "d0fc6802d6034d33bb33be048799320d", "21df32143aa4410c885c0b84ce68466a", "5dc3fb97742846c3bf4e6b99159d88ed", "16583bf31a1846e0926fcfc3690fa505", "b3638367ff0749ecabfef81e18c3f3dd", "c84a668f99be4a74aae5cca75a2448d8", "8dbb8561b0214721b69a0b4c41a9e431", "d5bdec8c59af4d3a94ed7d2bfa2b7921", "2d361e1d14c64f09b3dae11750e287f4", "1ecf09bddb4046218cc703af721dced3", "2361b35c79864c10a84b44929f69c547", "97b945925ec94ee8851187d97a3b3e01", "b332f446bf5a457bb1bb32862fb25779", "4408a069a4e341d59423b51c25afbd64", "f7902776f2a44de294a21f8d9d1f4e73", "667e586bf81d4f90b17247741fed6a16", "66b02dd825894df9b4c07fbf6a9d55b7", "ae9fdc486fee4a07a332d2767aa487d3", "b237fb86f5b24341a830c3707f881d12", "8c436fe0faee404e8d61d9afa916d6b3", "70141c664f464503b8a0821e6fb8aff2", "03d88b6a463c40febec9e44329493ed3", "443cae09fcc74bce8c85514c0e3306f8", "5e3c171ca5494501841b1cabe5c083fe", "ba6bf08f321841e180630e24ad14e31c", "c18a21dd2b4f4d53afcf25d5e2f282ba", "a61ba57420fc4fb387d03fbf2261e0d7", "fe2444e22fac4ce48a2ac8b9483dd0ab", "fe43c14445c54ea590a28bc3663d0c48", "47ade2b90f5d41469fe68aa452043b10", "3c4722f0972b4bbf995ea7285a61ba26", "981e19084d2c4c4da0285ab6758e53d0", "8ae036dfd56e4e92916f2030dbb5d634", "c90bdb60e0fc46d8aa1d9e858cbbdd07", "a7e36b913ce946e0808a5bc3f5b9744b", "1ea701fee9fa4aac92ae9750da494815", "c85f1cfff87b435f9041ecd4b657587b", "e4eb768259094b0ea344ac96181842b4", "51c2efe52566422394209040317384d9", "c48225673e394ae9873b25a8b13446b9", "9b97b57c43ec47ffad7e15ab3c7f21be", "5f810775e57943bdafcceeccca145450", "b1a430285adc498d836178ba93ec5604", "43c0b5f1598e487194f846977edb2d9e", "4f579cd077cd41c798478f572d592f6a", "56a5f8469b834a998dbf9b195828f3de", "e21d0116f78140cd9a2dfd548b93cdd9", "49639c7bec544b3d92340188e9fab090", "644eb1b7ad734624a51ec0c3435a96dc", "181c7cd4ce0140b7a7bcdb8bda323529", "4a9844c83b104b2dbb1b1149e894089b", "9156991030be4967a32b7014fbb9d202", "4e5e63413ead4256ad5900667f5c03b2", "d70e648f960c4f71b0c4b250fef32654", "ad3adfba64654cf2859f850c3de05e31", "a37acc4c0f11436c80cb72272709a9fe", "9b46b99cc8f5432cbce73a2913141033", "96cc9201587f47679d0262868277b160", "58691977879e424fa8d480be75a6e70d", "8edb9a9006df4929b41b6531d9c27c95", "cbd4cf8895514baa9420289c14455686", "397e656680024d148a597cad7def07f5", "c0297069253140978f1ccb5c1fed852c", "1808e709f81847cabc126abe8552c289", "f5117b4078194a058dac514abce9d08a", "ed18219a0316437d8aef630b5de85b75", "74742a6f4bb94067b6e6703cef8c3941", "d1abd20ebc684c839df31d06cb5d658f", "1d3238444dd041839602d3db4ed7fde2", "daaf3480c1794c8895049dcd8eec3573", "028dc1c882454b0dbbebdfbeb6eb4ca3", "28450d6b5cc145afb3f4fda0f133b7ac", "f5e7b16ff8f84ba888dd1af0e08df12d", "d7bcadb25ead446181103aaba00f52c0", "25f52c2d14c54eb09923acf20e85ca07", "8df67a8cbe0c4734b52754108642ac10", "04db919ebb474ae6bd1fba3cb9437eb6", "1a4c9e787979474b9049e36f8c07c12b", "3de2161fe36a43699a713916adc09857", "125518e68b4e491a8e6d40c40504a5fb", "fced4e62adb743798567c2c0f0d90966", "77c06023e71440308a6aedd86190ab9b", "2d508731af60413ea81136dc2ab64a76", "2036bb5ed5fa4bd49e1d76f2b713b6a1", "f9e6e2e5b59a4a0984ca0a83db0b186d", "5b0ca7559eea43edb7009bc8ad41dcd6", "db0696c568d34b6eab55305e2b87c1e2", "95d28ba0fd2c47e2bb1fc145d82c7164", "9430d398534341bca671d10e8bc91ddc", "020c2d09aa4941b5bddfb3ce9470f9e6", "bf309ecf24bb4ca68f72ce1acf72b739", "148b83fa058145b98b69e29cda2f9dcc", "e0ff84feca0f4c52b3c7d23cdff1a110", "5a5cf37b757f4af78a0ad69f0ed5bfbc", "65a2889ce3c04085a0702ce3bd75c9c9", "0f5aee18791e4f8d95f86f6254ac7878", "6fc02729c4254edcb86daf71336f30d6", "7f5ece3436c64e51b34c20503d2fc0c3", "d2c0dc378e864f42bc9994f7bc31ee1d"]}, "id": "QmUBVEnvCDJv", "outputId": "f63afa9e-e320-4da2-bb91-b7f4f9163379"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n", "🦥 Unsloth Zoo will now patch everything to make training faster!\n", "==((====))==  Unsloth 2024.11.9: Fast Mllama vision patching. Transformers = 4.46.2.\n", "   \\\\   /|    GPU: Tesla T4. Max memory: 14.748 GB. Platform = Linux.\n", "O^O/ \\_/ \\    Pytorch: 2.5.1+cu121. CUDA = 7.5. CUDA Toolkit = 12.1.\n", "\\        /    Bfloat16 = FALSE. FA [Xformers = 0.0.28.post3. FA2 = False]\n", " \"-____-\"     Free Apache license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "98d2cd94be9c454f83a18c8bf5fe9aec", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors.index.json:   0%|          | 0.00/385k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "289cd006e4ff4f018e758890e148c852", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading shards:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d0fc6802d6034d33bb33be048799320d", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00001-of-00002.safetensors:   0%|          | 0.00/5.00G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "97b945925ec94ee8851187d97a3b3e01", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00002-of-00002.safetensors:   0%|          | 0.00/2.18G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "443cae09fcc74bce8c85514c0e3306f8", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c90bdb60e0fc46d8aa1d9e858cbbdd07", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/210 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4f579cd077cd41c798478f572d592f6a", "version_major": 2, "version_minor": 0}, "text/plain": ["preprocessor_config.json:   0%|          | 0.00/477 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a37acc4c0f11436c80cb72272709a9fe", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/55.9k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "74742a6f4bb94067b6e6703cef8c3941", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/17.2M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1a4c9e787979474b9049e36f8c07c12b", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/454 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9430d398534341bca671d10e8bc91ddc", "version_major": 2, "version_minor": 0}, "text/plain": ["chat_template.json:   0%|          | 0.00/5.15k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from unsloth import FastVisionModel # FastLanguageModel for LLMs\n", "import torch\n", "\n", "# 4bit pre quantized models we support for 4x faster downloading + no OOMs.\n", "fourbit_models = [\n", "    \"unsloth/Llama-3.2-11B-Vision-Instruct-bnb-4bit\", # Llama 3.2 vision support\n", "    \"unsloth/Llama-3.2-11B-Vision-bnb-4bit\",\n", "    \"unsloth/Llama-3.2-90B-Vision-Instruct-bnb-4bit\", # Can fit in a 80GB card!\n", "    \"unsloth/Llama-3.2-90B-Vision-bnb-4bit\",\n", "\n", "    \"unsloth/Pixtral-12B-2409-bnb-4bit\",              # Pixtral fits in 16GB!\n", "    \"unsloth/Pixtral-12B-Base-2409-bnb-4bit\",         # Pixtral base model\n", "\n", "    \"unsloth/Qwen2-VL-2B-Instruct-bnb-4bit\",          # Qwen2 VL support\n", "    \"unsloth/Qwen2-VL-7B-Instruct-bnb-4bit\",\n", "    \"unsloth/Qwen2-VL-72B-Instruct-bnb-4bit\",\n", "\n", "    \"unsloth/llava-v1.6-mistral-7b-hf-bnb-4bit\",      # Any Llava variant works!\n", "    \"unsloth/llava-1.5-7b-hf-bnb-4bit\",\n", "] # More models at https://huggingface.co/unsloth\n", "\n", "model, tokenizer = FastVisionModel.from_pretrained(\n", "    \"unsloth/Llama-3.2-11B-Vision-Instruct\",\n", "    load_in_4bit = True, # Use 4bit to reduce memory use. False for 16bit LoRA.\n", "    use_gradient_checkpointing = \"unsloth\", # True or \"unsloth\" for long context\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "SXd9bTZd1aaL"}, "source": ["We now add LoRA adapters for parameter efficient finetuning - this allows us to only efficiently train 1% of all parameters.\n", "\n", "**[NEW]** We also support finetuning ONLY the vision part of the model, or ONLY the language part. Or you can select both! You can also select to finetune the attention or the MLP layers!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "6bZsfBuZDeCL"}, "outputs": [], "source": ["model = FastVisionModel.get_peft_model(\n", "    model,\n", "    finetune_vision_layers     = False, # False if not finetuning vision layers\n", "    finetune_language_layers   = True, # False if not finetuning language layers\n", "    finetune_attention_modules = True, # False if not finetuning attention layers\n", "    finetune_mlp_modules       = True, # False if not finetuning MLP layers\n", "\n", "    r = 16,           # The larger, the higher the accuracy, but might overfit\n", "    lora_alpha = 16,  # Recommended alpha == r at least\n", "    lora_dropout = 0,\n", "    bias = \"none\",\n", "    random_state = 3407,\n", "    use_rslora = False,  # We support rank stabilized LoRA\n", "    loftq_config = None, # And LoftQ\n", "    # target_modules = \"all-linear\", # Optional now! Can specify a list if needed\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "vITh0KVJ10qX"}, "source": ["<a name=\"Data\"></a>\n", "### Data Prep\n", "We'll be using a sampled version of the ROCO radiography dataset. You can access the dataset [here](https://huggingface.co/datasets/unsloth/Radiology_mini). The full dataset is [here](https://huggingface.co/datasets/eltorio/ROCOv2-radiology).\n", "\n", "The dataset includes X-rays, CT scans and ultrasounds showcasing medical conditions and diseases. Each image has a caption written by experts describing it. The goal is to finetune a VLM to make it a useful analysis tool for medical professionals.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 177, "referenced_widgets": ["aecf49a7a37445dfb4af43fed91cdc97", "209f0d74e16b4c03a72687d796958863", "17c372165582427dbc34abfd5dfdaa6c", "59110c931b0b45d5a61d7dae7c7e7120", "1ab79420b99e4508944ba5b4361171f8", "89284068a3be4af4a0d9b8bc50b9afba", "d11bd8033e4740929088a4fd0ef883c5", "cc1cf7cbbcae4771be85c3e628510997", "758e626f5c2f4b0193e1cf27824818ca", "dfdce9a9549448a8a475db2c2507f5eb", "4893fac70136425c9aa1c685e4ce71f3", "ee828b24ee424cfc82b2f3b87636940a", "115f636f0c0a4c1ebee21d973ff09e40", "adb8a39222db4fe880c180fe5c601dbe", "775514e06f1c453fa311d528b3d62749", "523f128189f746ce9fb7a594e72e9c7e", "72715b7c03e34b01b5f7715ce658eacd", "432103db24e448e8b62bce3af49ec581", "b382f1fd5c1645169079cdef08c14357", "5ad48ea79bde44639976eeb61377b74d", "2ae0d6c5c4c1419293e7b3832b8d8a17", "71f1e8c406a54c52b6983ff2b922be20", "34c7dbce08a146a7949152b0cac8666d", "f8e7b74b13864adc9d6cfccaa9a11362", "2aaa6c22a7d34c2aa20a169236a72f59", "c79fefc6a20d4bce9eca13b0b1a604aa", "8463516108b8491d8aaa0449a7f21f05", "cb71c9d8d57744fb9cfeb740d7ea50e6", "0241e7fc2a86481389ce140690fc544a", "315e97f8931941d382ae5b91e1d644a0", "ebdc57260e224077b0ba08ffbb3fb6a1", "0ee41ec935844b09850972773468b5de", "e7fabc6d4ea8476a9459604d745b7f08", "5034f195628e43b98e4ab33fd297308c", "46686703d557405685b5e910d81cfb4a", "55e11fafe31f4147a7a53c870f637527", "7fa57079e7414449a9b04330c91bd50d", "add5ec75448b4378b8e7aab92a5e53f5", "2e2dcbc6ae13435fa6bb91c35dbdf1ae", "64a4a67880614ecebad79f9ac957d4af", "dff190bbf4a746d393857037be5e5d72", "7ebc322bc4824599a6f59ed979eae207", "d61c2405111d4acc8428e1cebb96c963", "5263b46f262a40518c43778191394e4a", "78f33011fc0648439338fb4556284f31", "b08e04e041a84597b225f2194943d715", "7d36203af6344d50813eab42af8df957", "00714fb691c14ca6a9ee14ead204b6c0", "7d057295875d4525a165c823ff222268", "be882001119342ebbbbc3f73dbfa5356", "2713d41644f84b5290c0dd94b2953b2a", "235bc65f45ac4b369e1e03582b1b2061", "d2d708ed937b4deb8d72805e98b3aa9b", "c0fc8d1e49d643b0b9871080545cbb27", "f9bae803d6944df98773db211a4e98b6"]}, "id": "LjY75GoYUCB8", "outputId": "ac18735b-4865-4634-cdac-0672be9dc0e6"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "aecf49a7a37445dfb4af43fed91cdc97", "version_major": 2, "version_minor": 0}, "text/plain": ["README.md:   0%|          | 0.00/610 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ee828b24ee424cfc82b2f3b87636940a", "version_major": 2, "version_minor": 0}, "text/plain": ["train-00000-of-00001.parquet:   0%|          | 0.00/481M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "34c7dbce08a146a7949152b0cac8666d", "version_major": 2, "version_minor": 0}, "text/plain": ["test-00000-of-00001.parquet:   0%|          | 0.00/79.2M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5034f195628e43b98e4ab33fd297308c", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating train split:   0%|          | 0/1978 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "78f33011fc0648439338fb4556284f31", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating test split:   0%|          | 0/327 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from datasets import load_dataset\n", "\n", "dataset = load_dataset(\"unsloth/Radiology_mini\", split=\"train\")"]}, {"cell_type": "markdown", "metadata": {"id": "W1W2Qhsz6rUT"}, "source": ["Let's take a look at the dataset, and check what the 1st example shows:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "bfcSGwIb6p_R", "outputId": "111b6e00-b335-431c-c3a6-7e0d6702c056"}, "outputs": [{"data": {"text/plain": ["Dataset({\n", "    features: ['image', 'image_id', 'caption', 'cui'],\n", "    num_rows: 1978\n", "})"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 459}, "id": "uOLWY2936t1n", "outputId": "f0a03924-d626-441d-f8d4-746427e23b0c"}, "outputs": [{"data": {"image/jpeg": "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", "image/png": "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", "text/plain": ["<PIL.PngImagePlugin.PngImageFile image mode=L size=657x442>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset[0][\"image\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 53}, "id": "lXjfJr4W6z8P", "outputId": "05f346ff-3e4e-455b-f273-71429a91de26"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["'Panoramic radiography shows an osteolytic lesion in the right posterior maxilla with resorption of the floor of the maxillary sinus (arrows).'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset[0][\"caption\"]"]}, {"cell_type": "markdown", "metadata": {"id": "K9CBpiISFa6C"}, "source": ["To format the dataset, all vision finetuning tasks should be formatted as follows:\n", "\n", "```python\n", "[\n", "{ \"role\": \"user\",\n", "  \"content\": [{\"type\": \"text\",  \"text\": instruction}, {\"type\": \"image\", \"image\": image} ]\n", "},\n", "{ \"role\": \"assistant\",\n", "  \"content\": [{\"type\": \"text\",  \"text\": answer} ]\n", "},\n", "]\n", "```\n", "\n", "We will craft an custom instruction asking the VLM to be an expert radiographer. Notice also instead of just 1 instruction, you can add multiple turns to make it a dynamic conversation."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "oPXzJZzHEgXe"}, "outputs": [], "source": ["instruction = \"You are an expert radiographer. Describe accurately what you see in this image.\"\n", "\n", "def convert_to_conversation(sample):\n", "    conversation = [\n", "        { \"role\": \"user\",\n", "          \"content\" : [\n", "            {\"type\" : \"text\",  \"text\"  : instruction},\n", "            {\"type\" : \"image\", \"image\" : sample[\"image\"]} ]\n", "        },\n", "        { \"role\" : \"assistant\",\n", "          \"content\" : [\n", "            {\"type\" : \"text\",  \"text\"  : sample[\"caption\"]} ]\n", "        },\n", "    ]\n", "    return { \"messages\" : conversation }\n", "pass"]}, {"cell_type": "markdown", "metadata": {"id": "FY-9u-OD6_gE"}, "source": ["Let's convert the dataset into the \"correct\" format for finetuning:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "gFW2qXIr7Ezy"}, "outputs": [], "source": ["converted_dataset = [convert_to_conversation(sample) for sample in dataset]"]}, {"cell_type": "markdown", "metadata": {"id": "ndDUB23CGAC5"}, "source": ["The first example is now structured like below:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "gGFzmplrEy9I", "outputId": "a56fd110-056d-45d8-fbed-3bb694fb0d44"}, "outputs": [{"data": {"text/plain": ["{'messages': [{'role': 'user',\n", "   'content': [{'type': 'text',\n", "     'text': 'You are an expert radiographer. Describe accurately what you see in this image.'},\n", "    {'type': 'image',\n", "     'image': <PIL.PngImagePlugin.PngImageFile image mode=L size=657x442>}]},\n", "  {'role': 'assistant',\n", "   'content': [{'type': 'text',\n", "     'text': 'Panoramic radiography shows an osteolytic lesion in the right posterior maxilla with resorption of the floor of the maxillary sinus (arrows).'}]}]}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["converted_dataset[0]"]}, {"cell_type": "markdown", "metadata": {"id": "FecKS-dA82f5"}, "source": ["Before we do any finetuning, maybe the vision model already knows how to analyse the images? Let's check if this is the case!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "vcat4UxA81vr", "outputId": "4d32760d-9094-45d1-d014-012525958314"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["This radiograph appears to be a panoramic view of the upper and lower dentition, specifically an Orthopantomogram (OPG).\n", "\n", "* The panoramic radiograph demonstrates normal dental structures.\n", "* There is an abnormal area on the upper right, represented by an area of radiolucent bone, corresponding to the antrum.\n", "\n", "**Key Observations**\n", "\n", "* The bone between the left upper teeth is relatively radiopaque.\n", "* There are two large arrows above the image, suggesting the need for a closer examination of this area. One of the arrows is in a left-sided position, and the other is in the right-sided position. However, only\n"]}], "source": ["FastVisionModel.for_inference(model) # Enable for inference!\n", "\n", "image = dataset[0][\"image\"]\n", "instruction = \"You are an expert radiographer. Describe accurately what you see in this image.\"\n", "\n", "messages = [\n", "    {\"role\": \"user\", \"content\": [\n", "        {\"type\": \"image\"},\n", "        {\"type\": \"text\", \"text\": instruction}\n", "    ]}\n", "]\n", "input_text = tokenizer.apply_chat_template(messages, add_generation_prompt = True)\n", "inputs = tokenizer(\n", "    image,\n", "    input_text,\n", "    add_special_tokens = False,\n", "    return_tensors = \"pt\",\n", ").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer, skip_prompt = True)\n", "_ = model.generate(**inputs, streamer = text_streamer, max_new_tokens = 128,\n", "                   use_cache = True, temperature = 1.5, min_p = 0.1)"]}, {"cell_type": "markdown", "metadata": {"id": "idAEIeSQ3xdS"}, "source": ["<a name=\"<PERSON>\"></a>\n", "### Train the model\n", "Now let's use Huggingface TRL's `SFTTrainer`! More docs here: [TRL SFT docs](https://huggingface.co/docs/trl/sft_trainer). We do 60 steps to speed things up, but you can set `num_train_epochs=1` for a full run, and turn off `max_steps=None`. We also support TRL's `DPOTrainer`!\n", "\n", "We use our new `UnslothVisionDataCollator` which will help in our vision finetuning setup."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "95_Nn-89DhsL", "outputId": "27b03b47-2fab-4905-9408-e8fa30e8c7fd"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["max_steps is given, it will override any value given in num_train_epochs\n"]}], "source": ["from unsloth import is_bf16_supported\n", "from unsloth.trainer import UnslothVisionDataCollator\n", "from trl import SFTTrainer, SFTConfig\n", "\n", "FastVisionModel.for_training(model) # Enable for training!\n", "\n", "trainer = SFTT<PERSON>er(\n", "    model = model,\n", "    tokenizer = tokenizer,\n", "    data_collator = UnslothVisionDataCollator(model, tokenizer), # Must use!\n", "    train_dataset = converted_dataset,\n", "    args = SFTConfig(\n", "        per_device_train_batch_size = 2,\n", "        gradient_accumulation_steps = 4,\n", "        warmup_steps = 5,\n", "        max_steps = 30,\n", "        # num_train_epochs = 1, # Set this instead of max_steps for full training runs\n", "        learning_rate = 2e-4,\n", "        fp16 = not is_bf16_supported(),\n", "        bf16 = is_bf16_supported(),\n", "        logging_steps = 1,\n", "        optim = \"adamw_8bit\",\n", "        weight_decay = 0.01,\n", "        lr_scheduler_type = \"linear\",\n", "        seed = 3407,\n", "        output_dir = \"outputs\",\n", "        report_to = \"none\",     # For Weights and Biases\n", "\n", "        # You MUST put the below items for vision finetuning:\n", "        remove_unused_columns = False,\n", "        dataset_text_field = \"\",\n", "        dataset_kwargs = {\"skip_prepare_dataset\": True},\n", "        dataset_num_proc = 4,\n", "        max_seq_length = 2048,\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/"}, "id": "2ejIt2xSNKKp", "outputId": "e5294c5d-7420-4c1e-b4d8-c93caa5e384e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GPU = Tesla T4. Max memory = 14.748 GB.\n", "8.477 GB of memory reserved.\n"]}], "source": ["# @title Show current memory stats\n", "gpu_stats = torch.cuda.get_device_properties(0)\n", "start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)\n", "print(f\"GPU = {gpu_stats.name}. Max memory = {max_memory} GB.\")\n", "print(f\"{start_gpu_memory} GB of memory reserved.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "yqxqAZ7KJ4oL", "outputId": "108465c4-f25c-4c6a-e308-2937f9eb7fcd"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["==((====))==  Unsloth - 2x faster free finetuning | Num GPUs = 1\n", "   \\\\   /|    Num examples = 1,978 | Num Epochs = 1\n", "O^O/ \\_/ \\    Batch size per device = 2 | Gradient Accumulation steps = 4\n", "\\        /    Total batch size = 8 | Total steps = 30\n", " \"-____-\"     Number of trainable parameters = 67,174,400\n", "🦥 Un<PERSON><PERSON><PERSON> needs about 1-3 minutes to load everything - please wait!\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='30' max='30' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [30/30 14:55, Epo<PERSON> 0/1]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>3.611500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>3.613000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>3.488000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>3.600900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>3.031900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>2.884400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>2.446000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>2.323400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>1.984300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>1.647600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>1.613200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>1.516100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>1.629300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>1.463600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>1.500300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>1.439500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>1.375500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>1.322500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>1.352800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>1.394700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>1.527800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>1.386900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>1.570800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>1.209500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>1.345200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>1.388600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>1.416400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>1.392500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>1.375700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>1.252900</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["trainer_stats = trainer.train()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/"}, "id": "pCqnaKmlO1U9", "outputId": "888d5a4b-7c0d-485a-d347-efaaed70abe5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["957.9653 seconds used for training.\n", "15.97 minutes used for training.\n", "Peak reserved memory = 10.287 GB.\n", "Peak reserved memory for training = 1.81 GB.\n", "Peak reserved memory % of max memory = 69.752 %.\n", "Peak reserved memory for training % of max memory = 12.273 %.\n"]}], "source": ["# @title Show final memory and time stats\n", "used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "used_memory_for_lora = round(used_memory - start_gpu_memory, 3)\n", "used_percentage = round(used_memory / max_memory * 100, 3)\n", "lora_percentage = round(used_memory_for_lora / max_memory * 100, 3)\n", "print(f\"{trainer_stats.metrics['train_runtime']} seconds used for training.\")\n", "print(\n", "    f\"{round(trainer_stats.metrics['train_runtime']/60, 2)} minutes used for training.\"\n", ")\n", "print(f\"Peak reserved memory = {used_memory} GB.\")\n", "print(f\"Peak reserved memory for training = {used_memory_for_lora} GB.\")\n", "print(f\"Peak reserved memory % of max memory = {used_percentage} %.\")\n", "print(f\"Peak reserved memory for training % of max memory = {lora_percentage} %.\")"]}, {"cell_type": "markdown", "metadata": {"id": "ekOmTR1hSNcr"}, "source": ["<a name=\"Inference\"></a>\n", "### Inference\n", "Let's run the model! You can change the instruction and input - leave the output blank!\n", "\n", "We use `min_p = 0.1` and `temperature = 1.5`. Read this [Tweet](https://x.com/menhguin/status/1826132708508213629) for more information on why."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kR3gIAX-SM2q", "outputId": "ac901efd-de05-40ab-ac45-45c0f35ad9ff"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Panoramic radiography shows multiple small osteolytic lesions in the right mandibular ramus, condyle and ascending ramus, the left mandibular condyle, left first molar and body, right first and second premolar, left first, second and third molars (arrows).<|eot_id|>\n"]}], "source": ["FastVisionModel.for_inference(model) # Enable for inference!\n", "\n", "image = dataset[0][\"image\"]\n", "instruction = \"You are an expert radiographer. Describe accurately what you see in this image.\"\n", "\n", "messages = [\n", "    {\"role\": \"user\", \"content\": [\n", "        {\"type\": \"image\"},\n", "        {\"type\": \"text\", \"text\": instruction}\n", "    ]}\n", "]\n", "input_text = tokenizer.apply_chat_template(messages, add_generation_prompt = True)\n", "inputs = tokenizer(\n", "    image,\n", "    input_text,\n", "    add_special_tokens = False,\n", "    return_tensors = \"pt\",\n", ").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer, skip_prompt = True)\n", "_ = model.generate(**inputs, streamer = text_streamer, max_new_tokens = 128,\n", "                   use_cache = True, temperature = 1.5, min_p = 0.1)"]}, {"cell_type": "markdown", "metadata": {"id": "uMuVrWbjAzhc"}, "source": ["<a name=\"Save\"></a>\n", "### Saving, loading finetuned models\n", "To save the final model as LoRA adapters, either use Huggingface's `push_to_hub` for an online save or `save_pretrained` for a local save.\n", "\n", "**[NOTE]** This ONLY saves the LoRA adapters, and not the full model. To save to 16bit or GGUF, scroll down!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "upcOlWe7A1vc", "outputId": "f70e56fe-3cf1-4980-b259-ae2de09e824a"}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["model.save_pretrained(\"lora_model\")  # Local saving\n", "tokenizer.save_pretrained(\"lora_model\")\n", "# model.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving\n", "# tokenizer.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving"]}, {"cell_type": "markdown", "metadata": {"id": "AEEcJ4qfC7Lp"}, "source": ["Now if you want to load the LoRA adapters we just saved for inference, set `False` to `True`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "MKX_XKs_BNZR", "outputId": "d9c5f139-6084-431a-acc1-62f4d5d248ad"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Panoramic radiograph of the jaw showing well-defined radiolucency (arrowheads) of the right side and unerupted second permanent maxillary molar (arrow).<|eot_id|>\n"]}], "source": ["if False:\n", "    from unsloth import FastVisionModel\n", "    model, tokenizer = FastVisionModel.from_pretrained(\n", "        model_name = \"lora_model\", # YOUR MODEL YOU USED FOR TRAINING\n", "        load_in_4bit = True, # Set to False for 16bit LoRA\n", "    )\n", "    FastVisionModel.for_inference(model) # Enable for inference!\n", "\n", "image = dataset[0][\"image\"]\n", "instruction = \"You are an expert radiographer. Describe accurately what you see in this image.\"\n", "\n", "messages = [\n", "    {\"role\": \"user\", \"content\": [\n", "        {\"type\": \"image\"},\n", "        {\"type\": \"text\", \"text\": instruction}\n", "    ]}\n", "]\n", "input_text = tokenizer.apply_chat_template(messages, add_generation_prompt = True)\n", "inputs = tokenizer(\n", "    image,\n", "    input_text,\n", "    add_special_tokens = False,\n", "    return_tensors = \"pt\",\n", ").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer, skip_prompt = True)\n", "_ = model.generate(**inputs, streamer = text_streamer, max_new_tokens = 128,\n", "                   use_cache = True, temperature = 1.5, min_p = 0.1)"]}, {"cell_type": "markdown", "metadata": {"id": "f422JgM9sdVT"}, "source": ["### Saving to float16 for VLLM\n", "\n", "We also support saving to `float16` directly. Select `merged_16bit` for float16. Use `push_to_hub_merged` to upload to your Hugging Face account! You can go to https://huggingface.co/settings/tokens for your personal tokens."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "iHjt_SMYsd3P"}, "outputs": [], "source": ["# Select ONLY 1 to save! (Both not needed!)\n", "\n", "# Save locally to 16bit\n", "if False: model.save_pretrained_merged(\"unsloth_finetune\", tokenizer,)\n", "\n", "# To export and save to your Hugging Face account\n", "if False: model.push_to_hub_merged(\"YOUR_USERNAME/unsloth_finetune\", tokenizer, token = \"PUT_HERE\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And we're done! If you have any questions on Unsloth, we have a [Discord](https://discord.gg/unsloth) channel! If you find any bugs or want to keep updated with the latest LLM stuff, or need help, join projects etc, feel free to join our Discord!\n", "\n", "Some other links:\n", "1. Train your own reasoning model - Llama GRPO notebook [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.1_(8B)-GRPO.ipynb)\n", "2. Saving finetunes to Ollama. [Free notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb)\n", "3. Llama 3.2 Vision finetuning - Radiography use case. [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.2_(11B)-Vision.ipynb)\n", "6. See notebooks for DPO, ORPO, Continued pretraining, conversational finetuning and more on our [documentation](https://docs.unsloth.ai/get-started/unsloth-notebooks)!\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://unsloth.ai\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "  <a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord.png\" width=\"145\"></a>\n", "  <a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a>\n", "\n", "  Join Discord if you need help + ⭐️ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐️\n", "</div>\n"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"00714fb691c14ca6a9ee14ead204b6c0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c0fc8d1e49d643b0b9871080545cbb27", "placeholder": "​", "style": "IPY_MODEL_f9bae803d6944df98773db211a4e98b6", "value": " 327/327 [00:00&lt;00:00, 848.86 examples/s]"}}, "017cce265bf84be7bd9b8ebe5f25047e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "020c2d09aa4941b5bddfb3ce9470f9e6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5a5cf37b757f4af78a0ad69f0ed5bfbc", "placeholder": "​", "style": "IPY_MODEL_65a2889ce3c04085a0702ce3bd75c9c9", "value": "chat_template.json: 100%"}}, "0241e7fc2a86481389ce140690fc544a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "028dc1c882454b0dbbebdfbeb6eb4ca3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "03d88b6a463c40febec9e44329493ed3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "04db919ebb474ae6bd1fba3cb9437eb6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0d6664235d744c66bc567215699966b3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0ee41ec935844b09850972773468b5de": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0f5aee18791e4f8d95f86f6254ac7878": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "115f636f0c0a4c1ebee21d973ff09e40": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_72715b7c03e34b01b5f7715ce658eacd", "placeholder": "​", "style": "IPY_MODEL_432103db24e448e8b62bce3af49ec581", "value": "train-00000-of-00001.parquet: 100%"}}, "125518e68b4e491a8e6d40c40504a5fb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f9e6e2e5b59a4a0984ca0a83db0b186d", "max": 454, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_5b0ca7559eea43edb7009bc8ad41dcd6", "value": 454}}, "144522321d134e94858678d525a85f80": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ef9a483fb2a3478cb94331868ab2db90", "placeholder": "​", "style": "IPY_MODEL_ce61267651164b50aa49876a05b7296a", "value": " 385k/385k [00:00&lt;00:00, 1.83MB/s]"}}, "148b83fa058145b98b69e29cda2f9dcc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7f5ece3436c64e51b34c20503d2fc0c3", "placeholder": "​", "style": "IPY_MODEL_d2c0dc378e864f42bc9994f7bc31ee1d", "value": " 5.15k/5.15k [00:00&lt;00:00, 343kB/s]"}}, "14ec87fd00454cc9b6e75a37d9cf2c1c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "16583bf31a1846e0926fcfc3690fa505": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1ecf09bddb4046218cc703af721dced3", "placeholder": "​", "style": "IPY_MODEL_2361b35c79864c10a84b44929f69c547", "value": " 5.00G/5.00G [00:35&lt;00:00, 303MB/s]"}}, "17c372165582427dbc34abfd5dfdaa6c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cc1cf7cbbcae4771be85c3e628510997", "max": 610, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_758e626f5c2f4b0193e1cf27824818ca", "value": 610}}, "1808e709f81847cabc126abe8552c289": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "181c7cd4ce0140b7a7bcdb8bda323529": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1a4c9e787979474b9049e36f8c07c12b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_3de2161fe36a43699a713916adc09857", "IPY_MODEL_125518e68b4e491a8e6d40c40504a5fb", "IPY_MODEL_fced4e62adb743798567c2c0f0d90966"], "layout": "IPY_MODEL_77c06023e71440308a6aedd86190ab9b"}}, "1ab79420b99e4508944ba5b4361171f8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1d3238444dd041839602d3db4ed7fde2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d7bcadb25ead446181103aaba00f52c0", "max": 17210088, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_25f52c2d14c54eb09923acf20e85ca07", "value": 17210088}}, "1ea701fee9fa4aac92ae9750da494815": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9b97b57c43ec47ffad7e15ab3c7f21be", "max": 210, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_5f810775e57943bdafcceeccca145450", "value": 210}}, "1ecf09bddb4046218cc703af721dced3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2036bb5ed5fa4bd49e1d76f2b713b6a1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "209f0d74e16b4c03a72687d796958863": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_89284068a3be4af4a0d9b8bc50b9afba", "placeholder": "​", "style": "IPY_MODEL_d11bd8033e4740929088a4fd0ef883c5", "value": "README.md: 100%"}}, "21df32143aa4410c885c0b84ce68466a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c84a668f99be4a74aae5cca75a2448d8", "placeholder": "​", "style": "IPY_MODEL_8dbb8561b0214721b69a0b4c41a9e431", "value": "model-00001-of-00002.safetensors: 100%"}}, "235bc65f45ac4b369e1e03582b1b2061": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2361b35c79864c10a84b44929f69c547": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2394d94e0e714ffdb528b55605f40dc0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_24f8f69538bb469192088f3ffa1ed624", "max": 385193, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f5123643c11240c0bcc3d3588e8e8a1b", "value": 385193}}, "24f8f69538bb469192088f3ffa1ed624": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "25f52c2d14c54eb09923acf20e85ca07": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "2713d41644f84b5290c0dd94b2953b2a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "28450d6b5cc145afb3f4fda0f133b7ac": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "289cd006e4ff4f018e758890e148c852": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c8abe988d9864ca1955430b023c223ad", "IPY_MODEL_ad5361da5e774447b07bab7e0f272813", "IPY_MODEL_7fd8253214984e578754c1a7f184687e"], "layout": "IPY_MODEL_e84d7f0735e045f498a85deee72e4a36"}}, "2aaa6c22a7d34c2aa20a169236a72f59": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "danger", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_315e97f8931941d382ae5b91e1d644a0", "max": 79244825, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_ebdc57260e224077b0ba08ffbb3fb6a1", "value": 79244818}}, "2ae0d6c5c4c1419293e7b3832b8d8a17": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2d361e1d14c64f09b3dae11750e287f4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "2d508731af60413ea81136dc2ab64a76": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2e2dcbc6ae13435fa6bb91c35dbdf1ae": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "315e97f8931941d382ae5b91e1d644a0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "34c7dbce08a146a7949152b0cac8666d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_f8e7b74b13864adc9d6cfccaa9a11362", "IPY_MODEL_2aaa6c22a7d34c2aa20a169236a72f59", "IPY_MODEL_c79fefc6a20d4bce9eca13b0b1a604aa"], "layout": "IPY_MODEL_8463516108b8491d8aaa0449a7f21f05"}}, "397e656680024d148a597cad7def07f5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3c4722f0972b4bbf995ea7285a61ba26": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "3de2161fe36a43699a713916adc09857": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2d508731af60413ea81136dc2ab64a76", "placeholder": "​", "style": "IPY_MODEL_2036bb5ed5fa4bd49e1d76f2b713b6a1", "value": "special_tokens_map.json: 100%"}}, "432103db24e448e8b62bce3af49ec581": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "43c0b5f1598e487194f846977edb2d9e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4408a069a4e341d59423b51c25afbd64": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "danger", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b237fb86f5b24341a830c3707f881d12", "max": 2181540357, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_8c436fe0faee404e8d61d9afa916d6b3", "value": 2181540149}}, "443cae09fcc74bce8c85514c0e3306f8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_5e3c171ca5494501841b1cabe5c083fe", "IPY_MODEL_ba6bf08f321841e180630e24ad14e31c", "IPY_MODEL_c18a21dd2b4f4d53afcf25d5e2f282ba"], "layout": "IPY_MODEL_a61ba57420fc4fb387d03fbf2261e0d7"}}, "46686703d557405685b5e910d81cfb4a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2e2dcbc6ae13435fa6bb91c35dbdf1ae", "placeholder": "​", "style": "IPY_MODEL_64a4a67880614ecebad79f9ac957d4af", "value": "Generating train split: 100%"}}, "47ade2b90f5d41469fe68aa452043b10": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4893fac70136425c9aa1c685e4ce71f3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "49639c7bec544b3d92340188e9fab090": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d70e648f960c4f71b0c4b250fef32654", "placeholder": "​", "style": "IPY_MODEL_ad3adfba64654cf2859f850c3de05e31", "value": " 477/477 [00:00&lt;00:00, 36.9kB/s]"}}, "4a9844c83b104b2dbb1b1149e894089b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4e5e63413ead4256ad5900667f5c03b2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "4f579cd077cd41c798478f572d592f6a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_56a5f8469b834a998dbf9b195828f3de", "IPY_MODEL_e21d0116f78140cd9a2dfd548b93cdd9", "IPY_MODEL_49639c7bec544b3d92340188e9fab090"], "layout": "IPY_MODEL_644eb1b7ad734624a51ec0c3435a96dc"}}, "5034f195628e43b98e4ab33fd297308c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_46686703d557405685b5e910d81cfb4a", "IPY_MODEL_55e11fafe31f4147a7a53c870f637527", "IPY_MODEL_7fa57079e7414449a9b04330c91bd50d"], "layout": "IPY_MODEL_add5ec75448b4378b8e7aab92a5e53f5"}}, "51c2efe52566422394209040317384d9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "523f128189f746ce9fb7a594e72e9c7e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5263b46f262a40518c43778191394e4a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "55e11fafe31f4147a7a53c870f637527": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_dff190bbf4a746d393857037be5e5d72", "max": 1978, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7ebc322bc4824599a6f59ed979eae207", "value": 1978}}, "56a5f8469b834a998dbf9b195828f3de": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_181c7cd4ce0140b7a7bcdb8bda323529", "placeholder": "​", "style": "IPY_MODEL_4a9844c83b104b2dbb1b1149e894089b", "value": "preprocessor_config.json: 100%"}}, "58691977879e424fa8d480be75a6e70d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f5117b4078194a058dac514abce9d08a", "placeholder": "​", "style": "IPY_MODEL_ed18219a0316437d8aef630b5de85b75", "value": " 55.9k/55.9k [00:00&lt;00:00, 4.15MB/s]"}}, "59110c931b0b45d5a61d7dae7c7e7120": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_dfdce9a9549448a8a475db2c2507f5eb", "placeholder": "​", "style": "IPY_MODEL_4893fac70136425c9aa1c685e4ce71f3", "value": " 610/610 [00:00&lt;00:00, 9.82kB/s]"}}, "5a5cf37b757f4af78a0ad69f0ed5bfbc": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5ad48ea79bde44639976eeb61377b74d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "5b0ca7559eea43edb7009bc8ad41dcd6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "5dc3fb97742846c3bf4e6b99159d88ed": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "danger", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d5bdec8c59af4d3a94ed7d2bfa2b7921", "max": 4998395628, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2d361e1d14c64f09b3dae11750e287f4", "value": 4998395152}}, "5e3c171ca5494501841b1cabe5c083fe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fe2444e22fac4ce48a2ac8b9483dd0ab", "placeholder": "​", "style": "IPY_MODEL_fe43c14445c54ea590a28bc3663d0c48", "value": "Loading checkpoint shards: 100%"}}, "5f810775e57943bdafcceeccca145450": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "644eb1b7ad734624a51ec0c3435a96dc": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "64a4a67880614ecebad79f9ac957d4af": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "65a2889ce3c04085a0702ce3bd75c9c9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "667e586bf81d4f90b17247741fed6a16": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "66b02dd825894df9b4c07fbf6a9d55b7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6fc02729c4254edcb86daf71336f30d6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "70141c664f464503b8a0821e6fb8aff2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "71f1e8c406a54c52b6983ff2b922be20": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "72715b7c03e34b01b5f7715ce658eacd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "74742a6f4bb94067b6e6703cef8c3941": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_d1abd20ebc684c839df31d06cb5d658f", "IPY_MODEL_1d3238444dd041839602d3db4ed7fde2", "IPY_MODEL_daaf3480c1794c8895049dcd8eec3573"], "layout": "IPY_MODEL_028dc1c882454b0dbbebdfbeb6eb4ca3"}}, "758e626f5c2f4b0193e1cf27824818ca": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "775514e06f1c453fa311d528b3d62749": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2ae0d6c5c4c1419293e7b3832b8d8a17", "placeholder": "​", "style": "IPY_MODEL_71f1e8c406a54c52b6983ff2b922be20", "value": " 481M/481M [00:03&lt;00:00, 261MB/s]"}}, "77c06023e71440308a6aedd86190ab9b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "78f33011fc0648439338fb4556284f31": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b08e04e041a84597b225f2194943d715", "IPY_MODEL_7d36203af6344d50813eab42af8df957", "IPY_MODEL_00714fb691c14ca6a9ee14ead204b6c0"], "layout": "IPY_MODEL_7d057295875d4525a165c823ff222268"}}, "79e970816ead4141a8dc3c534db3a54c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7d057295875d4525a165c823ff222268": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7d36203af6344d50813eab42af8df957": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_235bc65f45ac4b369e1e03582b1b2061", "max": 327, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d2d708ed937b4deb8d72805e98b3aa9b", "value": 327}}, "7ebc322bc4824599a6f59ed979eae207": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7f5ece3436c64e51b34c20503d2fc0c3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7fa57079e7414449a9b04330c91bd50d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d61c2405111d4acc8428e1cebb96c963", "placeholder": "​", "style": "IPY_MODEL_5263b46f262a40518c43778191394e4a", "value": " 1978/1978 [00:05&lt;00:00, 295.72 examples/s]"}}, "7fd8253214984e578754c1a7f184687e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b0ea1c891cd44fb38f874d28f1b71ad7", "placeholder": "​", "style": "IPY_MODEL_017cce265bf84be7bd9b8ebe5f25047e", "value": " 2/2 [00:54&lt;00:00, 25.45s/it]"}}, "8463516108b8491d8aaa0449a7f21f05": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "89284068a3be4af4a0d9b8bc50b9afba": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8ae036dfd56e4e92916f2030dbb5d634": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8c436fe0faee404e8d61d9afa916d6b3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "8dbb8561b0214721b69a0b4c41a9e431": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8df67a8cbe0c4734b52754108642ac10": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8edb9a9006df4929b41b6531d9c27c95": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9156991030be4967a32b7014fbb9d202": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "91a165c1dade4c1b9d7c01771b61291e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9430d398534341bca671d10e8bc91ddc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_020c2d09aa4941b5bddfb3ce9470f9e6", "IPY_MODEL_bf309ecf24bb4ca68f72ce1acf72b739", "IPY_MODEL_148b83fa058145b98b69e29cda2f9dcc"], "layout": "IPY_MODEL_e0ff84feca0f4c52b3c7d23cdff1a110"}}, "95d28ba0fd2c47e2bb1fc145d82c7164": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "96cc9201587f47679d0262868277b160": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c0297069253140978f1ccb5c1fed852c", "max": 55931, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_1808e709f81847cabc126abe8552c289", "value": 55931}}, "97b945925ec94ee8851187d97a3b3e01": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b332f446bf5a457bb1bb32862fb25779", "IPY_MODEL_4408a069a4e341d59423b51c25afbd64", "IPY_MODEL_f7902776f2a44de294a21f8d9d1f4e73"], "layout": "IPY_MODEL_667e586bf81d4f90b17247741fed6a16"}}, "981e19084d2c4c4da0285ab6758e53d0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "98d2cd94be9c454f83a18c8bf5fe9aec": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c6d906d5fa284667a7021b56fb731e98", "IPY_MODEL_2394d94e0e714ffdb528b55605f40dc0", "IPY_MODEL_144522321d134e94858678d525a85f80"], "layout": "IPY_MODEL_eaa4f5b890c3436bb8da3e74a8eeb237"}}, "9b46b99cc8f5432cbce73a2913141033": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cbd4cf8895514baa9420289c14455686", "placeholder": "​", "style": "IPY_MODEL_397e656680024d148a597cad7def07f5", "value": "tokenizer_config.json: 100%"}}, "9b97b57c43ec47ffad7e15ab3c7f21be": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a37acc4c0f11436c80cb72272709a9fe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9b46b99cc8f5432cbce73a2913141033", "IPY_MODEL_96cc9201587f47679d0262868277b160", "IPY_MODEL_58691977879e424fa8d480be75a6e70d"], "layout": "IPY_MODEL_8edb9a9006df4929b41b6531d9c27c95"}}, "a61ba57420fc4fb387d03fbf2261e0d7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a7e36b913ce946e0808a5bc3f5b9744b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_51c2efe52566422394209040317384d9", "placeholder": "​", "style": "IPY_MODEL_c48225673e394ae9873b25a8b13446b9", "value": "generation_config.json: 100%"}}, "a85bb0dcd185406c885af8d491cf3c22": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ad3adfba64654cf2859f850c3de05e31": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ad5361da5e774447b07bab7e0f272813": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_79e970816ead4141a8dc3c534db3a54c", "max": 2, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f90c77c1bd02487da4d91289243c6a28", "value": 2}}, "adb8a39222db4fe880c180fe5c601dbe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "danger", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b382f1fd5c1645169079cdef08c14357", "max": 481222201, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_5ad48ea79bde44639976eeb61377b74d", "value": 481222156}}, "add5ec75448b4378b8e7aab92a5e53f5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ae9fdc486fee4a07a332d2767aa487d3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "aecf49a7a37445dfb4af43fed91cdc97": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_209f0d74e16b4c03a72687d796958863", "IPY_MODEL_17c372165582427dbc34abfd5dfdaa6c", "IPY_MODEL_59110c931b0b45d5a61d7dae7c7e7120"], "layout": "IPY_MODEL_1ab79420b99e4508944ba5b4361171f8"}}, "b08e04e041a84597b225f2194943d715": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_be882001119342ebbbbc3f73dbfa5356", "placeholder": "​", "style": "IPY_MODEL_2713d41644f84b5290c0dd94b2953b2a", "value": "Generating test split: 100%"}}, "b0ea1c891cd44fb38f874d28f1b71ad7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b1a430285adc498d836178ba93ec5604": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b237fb86f5b24341a830c3707f881d12": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b332f446bf5a457bb1bb32862fb25779": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_66b02dd825894df9b4c07fbf6a9d55b7", "placeholder": "​", "style": "IPY_MODEL_ae9fdc486fee4a07a332d2767aa487d3", "value": "model-00002-of-00002.safetensors: 100%"}}, "b3638367ff0749ecabfef81e18c3f3dd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b382f1fd5c1645169079cdef08c14357": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ba6bf08f321841e180630e24ad14e31c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_47ade2b90f5d41469fe68aa452043b10", "max": 2, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_3c4722f0972b4bbf995ea7285a61ba26", "value": 2}}, "be882001119342ebbbbc3f73dbfa5356": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bf309ecf24bb4ca68f72ce1acf72b739": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0f5aee18791e4f8d95f86f6254ac7878", "max": 5151, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_6fc02729c4254edcb86daf71336f30d6", "value": 5151}}, "c0297069253140978f1ccb5c1fed852c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c0fc8d1e49d643b0b9871080545cbb27": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c18a21dd2b4f4d53afcf25d5e2f282ba": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_981e19084d2c4c4da0285ab6758e53d0", "placeholder": "​", "style": "IPY_MODEL_8ae036dfd56e4e92916f2030dbb5d634", "value": " 2/2 [00:36&lt;00:00, 17.08s/it]"}}, "c48225673e394ae9873b25a8b13446b9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c6d906d5fa284667a7021b56fb731e98": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a85bb0dcd185406c885af8d491cf3c22", "placeholder": "​", "style": "IPY_MODEL_91a165c1dade4c1b9d7c01771b61291e", "value": "model.safetensors.index.json: 100%"}}, "c79fefc6a20d4bce9eca13b0b1a604aa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0ee41ec935844b09850972773468b5de", "placeholder": "​", "style": "IPY_MODEL_e7fabc6d4ea8476a9459604d745b7f08", "value": " 79.2M/79.2M [00:01&lt;00:00, 46.7MB/s]"}}, "c84a668f99be4a74aae5cca75a2448d8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c85f1cfff87b435f9041ecd4b657587b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b1a430285adc498d836178ba93ec5604", "placeholder": "​", "style": "IPY_MODEL_43c0b5f1598e487194f846977edb2d9e", "value": " 210/210 [00:00&lt;00:00, 14.5kB/s]"}}, "c8abe988d9864ca1955430b023c223ad": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0d6664235d744c66bc567215699966b3", "placeholder": "​", "style": "IPY_MODEL_14ec87fd00454cc9b6e75a37d9cf2c1c", "value": "Downloading shards: 100%"}}, "c90bdb60e0fc46d8aa1d9e858cbbdd07": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_a7e36b913ce946e0808a5bc3f5b9744b", "IPY_MODEL_1ea701fee9fa4aac92ae9750da494815", "IPY_MODEL_c85f1cfff87b435f9041ecd4b657587b"], "layout": "IPY_MODEL_e4eb768259094b0ea344ac96181842b4"}}, "cb71c9d8d57744fb9cfeb740d7ea50e6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cbd4cf8895514baa9420289c14455686": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cc1cf7cbbcae4771be85c3e628510997": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ce61267651164b50aa49876a05b7296a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d0fc6802d6034d33bb33be048799320d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_21df32143aa4410c885c0b84ce68466a", "IPY_MODEL_5dc3fb97742846c3bf4e6b99159d88ed", "IPY_MODEL_16583bf31a1846e0926fcfc3690fa505"], "layout": "IPY_MODEL_b3638367ff0749ecabfef81e18c3f3dd"}}, "d11bd8033e4740929088a4fd0ef883c5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d1abd20ebc684c839df31d06cb5d658f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_28450d6b5cc145afb3f4fda0f133b7ac", "placeholder": "​", "style": "IPY_MODEL_f5e7b16ff8f84ba888dd1af0e08df12d", "value": "tokenizer.json: 100%"}}, "d2c0dc378e864f42bc9994f7bc31ee1d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d2d708ed937b4deb8d72805e98b3aa9b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d5bdec8c59af4d3a94ed7d2bfa2b7921": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d61c2405111d4acc8428e1cebb96c963": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d70e648f960c4f71b0c4b250fef32654": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d7bcadb25ead446181103aaba00f52c0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "daaf3480c1794c8895049dcd8eec3573": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8df67a8cbe0c4734b52754108642ac10", "placeholder": "​", "style": "IPY_MODEL_04db919ebb474ae6bd1fba3cb9437eb6", "value": " 17.2M/17.2M [00:00&lt;00:00, 43.8MB/s]"}}, "db0696c568d34b6eab55305e2b87c1e2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dfdce9a9549448a8a475db2c2507f5eb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dff190bbf4a746d393857037be5e5d72": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e0ff84feca0f4c52b3c7d23cdff1a110": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e21d0116f78140cd9a2dfd548b93cdd9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9156991030be4967a32b7014fbb9d202", "max": 477, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_4e5e63413ead4256ad5900667f5c03b2", "value": 477}}, "e4eb768259094b0ea344ac96181842b4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e7fabc6d4ea8476a9459604d745b7f08": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e84d7f0735e045f498a85deee72e4a36": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "eaa4f5b890c3436bb8da3e74a8eeb237": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ebdc57260e224077b0ba08ffbb3fb6a1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "ed18219a0316437d8aef630b5de85b75": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ee828b24ee424cfc82b2f3b87636940a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_115f636f0c0a4c1ebee21d973ff09e40", "IPY_MODEL_adb8a39222db4fe880c180fe5c601dbe", "IPY_MODEL_775514e06f1c453fa311d528b3d62749"], "layout": "IPY_MODEL_523f128189f746ce9fb7a594e72e9c7e"}}, "ef9a483fb2a3478cb94331868ab2db90": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f5117b4078194a058dac514abce9d08a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f5123643c11240c0bcc3d3588e8e8a1b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f5e7b16ff8f84ba888dd1af0e08df12d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f7902776f2a44de294a21f8d9d1f4e73": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_70141c664f464503b8a0821e6fb8aff2", "placeholder": "​", "style": "IPY_MODEL_03d88b6a463c40febec9e44329493ed3", "value": " 2.18G/2.18G [00:17&lt;00:00, 66.5MB/s]"}}, "f8e7b74b13864adc9d6cfccaa9a11362": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cb71c9d8d57744fb9cfeb740d7ea50e6", "placeholder": "​", "style": "IPY_MODEL_0241e7fc2a86481389ce140690fc544a", "value": "test-00000-of-00001.parquet: 100%"}}, "f90c77c1bd02487da4d91289243c6a28": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f9bae803d6944df98773db211a4e98b6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f9e6e2e5b59a4a0984ca0a83db0b186d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fced4e62adb743798567c2c0f0d90966": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_db0696c568d34b6eab55305e2b87c1e2", "placeholder": "​", "style": "IPY_MODEL_95d28ba0fd2c47e2bb1fc145d82c7164", "value": " 454/454 [00:00&lt;00:00, 18.7kB/s]"}}, "fe2444e22fac4ce48a2ac8b9483dd0ab": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fe43c14445c54ea590a28bc3663d0c48": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "state": {}}}}, "nbformat": 4, "nbformat_minor": 0}