#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
读取JSON文件并使用VLLM的Qwen3-8B-AWQ模型进行高效并发中文翻译
支持断点续存、进度显示、自动备份等功能
"""

import json
import requests
import time
import os
from typing import List, Dict, Any
import logging
import re
import shutil
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 配置tqdm进度条样式
def setup_progress_bars():
    """配置进度条的全局样式"""
    # 设置tqdm的默认参数
    tqdm.pandas()
    # 禁用tqdm的警告
    import warnings
    warnings.filterwarnings("ignore", category=UserWarning, module="tqdm")

class VLLMTranslator:
    """使用VLLM API进行翻译的类，支持并发翻译"""

    def __init__(self, model_name: str = "/home/<USER>/Model/LLM/Qwen/Qwen3-4B-AWQ", base_url: str = "http://localhost:8666", max_concurrent: int = 10):
        self.model_name = model_name
        self.base_url = base_url
        self.api_url = f"{base_url}/v1/chat/completions"
        self.max_concurrent = max_concurrent
        self.session = requests.Session()  # 复用连接
        self.rate_limiter = threading.Semaphore(max_concurrent)  # 限制并发数

        # 超时配置
        self.connect_timeout = 30  # 连接超时
        self.read_timeout = 300    # 读取超时（5分钟）
        self.total_timeout = 360   # 总超时（6分钟）

        # 配置session
        self.session.headers.update({
            'Connection': 'keep-alive',
            'Content-Type': 'application/json'
        })

        # 设置连接池参数
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=max_concurrent,
            pool_maxsize=max_concurrent * 2,
            max_retries=0  # 我们自己处理重试
        )
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)

    def translate_text(self, text: str, field_type: str = "general", max_retries: int = 3) -> str:
        """
        使用VLLM翻译文本到中文（线程安全版本）

        Args:
            text: 要翻译的文本
            field_type: 字段类型 ("problem", "solution", "answer", "general")
            max_retries: 最大重试次数

        Returns:
            翻译后的中文文本
        """
        if not text or not str(text).strip():
            return ""

        with self.rate_limiter:  # 限制并发数
            return self._translate_single(text, field_type, max_retries)

    def _translate_single(self, text: str, field_type: str = "general", max_retries: int = 3) -> str:
        """内部翻译方法"""
        # 根据字段类型构建不同的翻译提示
        if field_type == "problem":
            prompt = f"""请将以下数学问题翻译成中文，要求：
1. 保持数学公式、符号、数字完全不变
2. 准确传达问题的数学含义
3. 使用标准的数学中文表达
4. 不要添加任何解释或说明

英文原文：
{text}

请直接输出中文翻译：

/no_think"""
        elif field_type == "solution":
            prompt = f"""请将以下数学解答过程翻译成中文，要求：
1. 保持所有数学公式、符号、数字、计算步骤完全不变
2. 准确翻译解题思路和推理过程
3. 保持逻辑结构清晰
4. 不要改变原有的解题方法
5. 不要添加额外的解释
6. 重要：如果内容包含<think>...</think>标签，请保留这些标签并翻译标签内的内容

英文原文：
{text}

请直接输出中文翻译：

/no_think"""
        elif field_type == "answer":
            prompt = f"""请将以下答案翻译成中文，要求：
1. 如果是纯数字、公式或符号，保持完全不变
2. 如果包含文字说明，只翻译文字部分
3. 保持答案的准确性和简洁性
4. 不要添加任何解释

英文原文：
{text}

请直接输出中文翻译：

/no_think"""
        else:
            prompt = f"""请将以下英文内容翻译成中文，保持数学公式、数字、符号不变。

英文原文：
{text}

请直接输出中文翻译，不要包含任何解释、说明或思考过程：

/no_think"""

        payload = {
            "model": self.model_name,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 20480,
            "stream": False,
            "extra_body": {
                "chat_template_kwargs": {
                    "enable_thinking": False
                }
            }
        }

        for attempt in range(max_retries):
            try:
                # 使用分离的超时设置
                timeout = (self.connect_timeout, self.read_timeout)

                response = self.session.post(
                    self.api_url,
                    json=payload,
                    timeout=timeout
                )
                response.raise_for_status()

                result = response.json()
                translated_text = result['choices'][0]['message']['content'].strip()

                # 清理思考标签和前缀
                # 只有在非solution字段时才移除 <think>...</think> 标签
                if field_type != "solution":
                    translated_text = re.sub(r'<think>.*?</think>', '', translated_text, flags=re.DOTALL).strip()

                # 清理可能的前缀
                if translated_text.startswith("中文翻译："):
                    translated_text = translated_text[5:].strip()
                elif translated_text.startswith("翻译："):
                    translated_text = translated_text[3:].strip()

                if translated_text:
                    return translated_text
                else:
                    logger.warning("翻译结果为空")

            except requests.exceptions.Timeout as e:
                logger.error(f"超时错误 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    wait_time = min(2 ** attempt, 30)  # 最大等待30秒
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)

            except requests.exceptions.ConnectionError as e:
                logger.error(f"连接错误 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    wait_time = min(2 ** attempt, 30)
                    logger.info(f"连接失败，等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)

            except requests.exceptions.RequestException as e:
                logger.error(f"请求错误 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    wait_time = min(2 ** attempt, 30)
                    time.sleep(wait_time)

            except Exception as e:
                logger.error(f"翻译错误 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    wait_time = min(2 ** attempt, 30)
                    time.sleep(wait_time)

        logger.error("翻译失败，返回原文")
        return text  # 如果翻译失败，返回原文

    def check_vllm_connection(self) -> bool:
        """检查VLLM服务是否可用"""
        try:
            # 测试简单的请求
            test_payload = {
                "model": self.model_name,
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10,
                "temperature": 0.1
            }

            timeout = (self.connect_timeout, min(self.read_timeout, 30))  # 测试时使用较短的超时
            response = self.session.post(
                self.api_url,
                json=test_payload,
                timeout=timeout
            )
            response.raise_for_status()

            logger.info(f"VLLM服务正常，模型 {self.model_name} 可用")
            return True

        except Exception as e:
            logger.error(f"无法连接到VLLM服务: {e}")
            return False

    def translate_batch(self, texts_with_types: List[tuple], max_workers: int = None, progress_bar=None) -> List[str]:
        """
        批量并发翻译文本

        Args:
            texts_with_types: (text, field_type) 元组列表
            max_workers: 最大工作线程数，默认为max_concurrent
            progress_bar: 外部传入的进度条对象

        Returns:
            翻译后的文本列表
        """
        if not texts_with_types:
            return []

        if max_workers is None:
            max_workers = self.max_concurrent

        results = [""] * len(texts_with_types)  # 预分配结果列表
        completed_count = 0
        failed_count = 0

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有翻译任务
            future_to_index = {
                executor.submit(self.translate_text, text, field_type): i
                for i, (text, field_type) in enumerate(texts_with_types)
                if text and str(text).strip()
            }

            # 收集结果
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    result = future.result()
                    results[index] = result
                    # 检查是否翻译成功（结果不等于原文）
                    original_text = texts_with_types[index][0]
                    if result == str(original_text):
                        failed_count += 1
                except Exception as e:
                    logger.error(f"批量翻译第{index}项失败: {e}")
                    results[index] = str(texts_with_types[index][0])  # 失败时返回原文
                    failed_count += 1

                completed_count += 1
                if progress_bar:
                    progress_bar.update(1)
                    success_rate = ((completed_count - failed_count) / completed_count * 100) if completed_count > 0 else 0
                    progress_bar.set_postfix({
                        "已完成": f"{completed_count}/{len(future_to_index)}",
                        "成功率": f"{success_rate:.1f}%",
                        "失败": failed_count
                    })

        return results

    def translate_json_item(self, prompt: str, max_retries: int = 3) -> dict:
        """
        翻译JSON条目，返回完整的JSON对象

        Args:
            prompt: 包含原始JSON和翻译要求的提示
            max_retries: 最大重试次数

        Returns:
            翻译后的JSON对象，如果失败返回None
        """
        payload = {
            "model": self.model_name,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 20480,  # 增加token限制以容纳完整JSON
            "stream": False,
            "extra_body": {
                "chat_template_kwargs": {
                    "enable_thinking": False
                }
            }
        }

        for attempt in range(max_retries):
            try:
                with self.rate_limiter:  # 限制并发数
                    timeout = (self.connect_timeout, self.read_timeout)

                    response = self.session.post(
                        self.api_url,
                        json=payload,
                        timeout=timeout
                    )
                    response.raise_for_status()

                    result = response.json()
                    translated_text = result['choices'][0]['message']['content'].strip()

                    # 清理思考标签和其他无效内容（JSON翻译时保留<think>标签）
                    translated_text = self._clean_response_text(translated_text, preserve_think=True)

                    # 尝试解析JSON
                    try:
                        # 提取JSON部分
                        json_str = self._extract_json_from_response(translated_text)

                        if json_str:
                            # 尝试解析JSON
                            parsed_json = json.loads(json_str)
                            return parsed_json
                        else:
                            logger.warning(f"无法从响应中提取JSON (尝试 {attempt + 1})")
                            if attempt < max_retries - 1:
                                continue

                    except json.JSONDecodeError as e:
                        logger.warning(f"JSON解析失败 (尝试 {attempt + 1}): {e}")
                        logger.warning(f"原始响应: {translated_text[:500]}...")
                        if attempt < max_retries - 1:
                            continue

            except requests.exceptions.Timeout as e:
                logger.error(f"超时错误 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    wait_time = min(2 ** attempt, 30)
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)

            except requests.exceptions.ConnectionError as e:
                logger.error(f"连接错误 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    wait_time = min(2 ** attempt, 30)
                    time.sleep(wait_time)

            except Exception as e:
                logger.error(f"翻译错误 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    wait_time = min(2 ** attempt, 30)
                    time.sleep(wait_time)

        logger.error("JSON翻译失败，返回None")
        return None

    def _clean_response_text(self, text: str, preserve_think: bool = False) -> str:
        """清理响应文本中的无效字符和标签"""
        # 对于JSON翻译，我们需要特殊处理
        if preserve_think:
            # 首先尝试找到JSON部分
            json_start = text.find('{')
            if json_start != -1:
                # 如果找到JSON开始，移除JSON之前的所有<think>标签（这些是模型生成的占位符）
                before_json = text[:json_start]
                json_part = text[json_start:]

                # 清理JSON之前的<think>标签（模型生成的占位符）
                before_json = re.sub(r'<think>.*?</think>', '', before_json, flags=re.DOTALL)
                before_json = re.sub(r'</think>', '', before_json)

                # 在JSON部分，我们需要区分模型生成的空<think>标签和原始数据中的<think>标签
                # 移除空的<think></think>占位符，但保留有内容的<think>标签
                json_part = re.sub(r'<think>\s*</think>', '', json_part, flags=re.DOTALL)

                text = before_json + json_part
            else:
                # 如果没有找到JSON，移除空的<think>占位符，保留有内容的<think>标签
                text = re.sub(r'<think>\s*</think>', '', text, flags=re.DOTALL)
        else:
            # 移除所有思考标签及其内容
            text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
            text = re.sub(r'</think>', '', text)
            # 移除其他可能的标签
            text = re.sub(r'<[^>]+>', '', text)

        # 移除控制字符（保留换行符和制表符）
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)

        # 清理多余的空白字符
        text = re.sub(r'\n\s*\n', '\n', text)
        text = text.strip()

        return text

    def _extract_json_from_response(self, text: str) -> str:
        """从响应文本中提取JSON字符串"""
        # 方法1: 查找代码块中的JSON
        if '```json' in text:
            start = text.find('```json') + 7
            end = text.find('```', start)
            if end != -1:
                return text[start:end].strip()
            else:
                return text[start:].strip()

        # 方法2: 查找普通代码块
        if '```' in text:
            start = text.find('```') + 3
            end = text.find('```', start)
            if end != -1:
                return text[start:end].strip()
            else:
                return text[start:].strip()

        # 方法3: 查找JSON对象（以{开始，以}结束）- 使用括号匹配
        start_idx = text.find('{')
        if start_idx != -1:
            # 使用括号匹配找到正确的结束位置
            brace_count = 0
            end_idx = start_idx
            for i in range(start_idx, len(text)):
                if text[i] == '{':
                    brace_count += 1
                elif text[i] == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        end_idx = i
                        break

            if brace_count == 0:
                json_str = text[start_idx:end_idx+1].strip()
                # 验证提取的JSON是否有效
                try:
                    json.loads(json_str)
                    return json_str
                except json.JSONDecodeError:
                    # 如果解析失败，尝试清理内容
                    return self._fix_nested_json(json_str)

        # 方法4: 如果都没找到，返回整个文本
        return text.strip()

    def _fix_nested_json(self, json_str: str) -> str:
        """修复嵌套JSON问题"""
        try:
            # 尝试解析JSON
            json.loads(json_str)
            return json_str
        except json.JSONDecodeError:
            # 如果解析失败，尝试修复常见问题

            # 问题1: generated_solution字段中包含嵌套的JSON
            # 查找 "generated_solution": " 后面的内容
            pattern = r'"generated_solution":\s*"([^"]*(?:\\.[^"]*)*)"'
            match = re.search(pattern, json_str)
            if match:
                solution_content = match.group(1)
                # 如果solution_content中包含JSON结构，需要转义
                if '{' in solution_content and '}' in solution_content:
                    # 转义引号和换行符
                    escaped_content = solution_content.replace('\\', '\\\\').replace('"', '\\"').replace('\n', '\\n')
                    json_str = json_str.replace(match.group(0), f'"generated_solution": "{escaped_content}"')

            # 问题2: 移除字段值中的控制字符
            json_str = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', json_str)

            # 问题3: 修复可能的多余逗号
            json_str = re.sub(r',\s*}', '}', json_str)
            json_str = re.sub(r',\s*]', ']', json_str)

            return json_str


class JsonTranslator:
    """处理JSON文件翻译的主类 - 按row_number单条目分块版本"""

    def __init__(self, file_path: str, translator: VLLMTranslator):
        self.file_path = file_path
        self.translator = translator
        self.data = None
        self.chunks_dir = "json_translation_chunks"
        self.progress_dir = "json_translation_progress"
        self.state_file = os.path.join(self.progress_dir, "json_translation_state.json")
        os.makedirs(self.chunks_dir, exist_ok=True)
        os.makedirs(self.progress_dir, exist_ok=True)

        # 进度跟踪变量
        self.total_tasks = 0
        self.completed_tasks = 0
        self.start_time = None

    def load_json(self) -> bool:
        """加载JSON文件"""
        try:
            logger.info(f"正在加载文件: {self.file_path}")
            with open(self.file_path, 'r', encoding='utf-8') as f:
                self.data = json.load(f)

            if isinstance(self.data, list):
                logger.info(f"文件加载成功! 数据条目数: {len(self.data)}")
                if len(self.data) > 0:
                    logger.info(f"数据字段: {list(self.data[0].keys())}")
            else:
                logger.error("JSON文件格式错误，应该是一个列表")
                return False
            return True
        except Exception as e:
            logger.error(f"加载文件失败: {e}")
            return False

    def split_into_chunks(self) -> List[str]:
        """根据row_number将每个条目分割成单独的文件"""
        if self.data is None:
            logger.error("请先加载JSON文件")
            return []

        # 确保块文件目录存在
        os.makedirs(self.chunks_dir, exist_ok=True)

        chunk_files = []
        total_items = len(self.data)

        logger.info(f"按row_number分块配置:")
        logger.info(f"- 总条目数: {total_items}")
        logger.info(f"- 分块方式: 每个条目一个文件")
        logger.info(f"- 将生成: {total_items} 个文件")

        with tqdm(total=total_items, desc="✂️ 按row_number分割", unit="文件", colour='orange') as pbar:
            for i, item in enumerate(self.data):
                # 获取row_number作为文件标识
                row_number = item.get('row_number', i)

                # 生成块文件名（每个文件包含一个条目）
                chunk_filename = f"chunk_row_{row_number:06d}.json"
                chunk_path = os.path.join(self.chunks_dir, chunk_filename)

                # 保存单个条目到文件（注意：保存为单个对象，不是数组）
                with open(chunk_path, 'w', encoding='utf-8') as f:
                    json.dump(item, f, ensure_ascii=False, indent=2)

                chunk_files.append(chunk_path)

                pbar.update(1)
                pbar.set_postfix({
                    "row_number": row_number,
                    "已生成": f"{i+1}/{total_items}"
                })

        logger.info(f"按row_number分块完成，生成 {len(chunk_files)} 个文件")
        return chunk_files



    def get_existing_chunks(self) -> List[str]:
        """获取已存在的块文件"""
        if not os.path.exists(self.chunks_dir):
            return []

        chunk_files = []
        for filename in sorted(os.listdir(self.chunks_dir)):
            if filename.startswith("chunk_row_") and filename.endswith(".json"):
                chunk_files.append(os.path.join(self.chunks_dir, filename))

        return chunk_files

    def cleanup_chunks(self):
        """清理临时块文件"""
        if os.path.exists(self.chunks_dir):
            shutil.rmtree(self.chunks_dir)
            logger.info("临时块文件已清理")

    def backup_original_file(self):
        """备份原始文件"""
        backup_path = f"{self.file_path}.backup"
        if not os.path.exists(backup_path):
            shutil.copy2(self.file_path, backup_path)
            logger.info(f"原始文件已备份到: {backup_path}")
        else:
            logger.info("备份文件已存在，跳过备份")

    def translate_chunk_file(self, chunk_path: str, fields_to_translate: List[str], overall_progress=None) -> bool:
        """翻译单个条目文件（一次API调用处理整个文件）"""
        try:
            # 读取单个条目文件
            with open(chunk_path, 'r', encoding='utf-8') as f:
                item = json.load(f)

            chunk_name = os.path.basename(chunk_path)
            row_number = item.get('row_number', 'unknown')

            # 检查是否已经翻译过（通过检查是否包含中文字符来判断）
            already_translated = True
            for field in fields_to_translate:
                if field in item and item[field]:
                    text = str(item[field])
                    # 简单检查是否包含中文字符
                    if not any('\u4e00' <= char <= '\u9fff' for char in text):
                        already_translated = False
                        break

            if already_translated:
                logger.debug(f"文件 {chunk_name} (row {row_number}) 已翻译，跳过")
                if overall_progress:
                    overall_progress.update(1)
                return True

            # 构建翻译提示，让API返回完整的JSON
            prompt = self._build_translation_prompt(item, fields_to_translate)

            # 一次API调用翻译整个条目
            translated_json = self.translator.translate_json_item(prompt)

            if translated_json:
                # 直接覆盖写入翻译后的JSON
                with open(chunk_path, 'w', encoding='utf-8') as f:
                    json.dump(translated_json, f, ensure_ascii=False, indent=2)

                if overall_progress:
                    overall_progress.update(1)
                return True
            else:
                logger.error(f"翻译文件 {chunk_name} 失败：API返回空结果")
                return False

        except Exception as e:
            logger.error(f"翻译文件 {chunk_path} 失败: {e}")
            return False

    def _build_translation_prompt(self, item: dict, fields_to_translate: List[str]) -> str:
        """构建翻译提示，要求API返回完整的JSON格式"""
        # 构建需要翻译的字段信息
        fields_info = []
        for field in fields_to_translate:
            if field in item and item[field] and str(item[field]).strip():
                field_type = self._get_field_type(field)
                fields_info.append(f'"{field}": 需要翻译为中文（类型：{field_type}）')

        prompt = f"""你是一个专业的JSON翻译器。请将以下JSON对象中的指定字段翻译成中文，并返回完整的JSON格式。

原始JSON：
{json.dumps(item, ensure_ascii=False, indent=2)}

翻译要求：
{chr(10).join(fields_info)}

翻译规则：
1. 保持所有数学公式、符号、数字完全不变
2. 直接将指定字段的内容翻译为中文，覆盖原字段内容
3. 保持其他字段不变
4. 返回完整的JSON对象，字段名保持不变，只翻译字段内容
5. 不要添加任何解释、说明、思考过程或其他文本
6. 不要使用代码块格式，直接返回纯JSON
7. 重要：字段值必须是纯文本字符串，不要在字段值中嵌套JSON对象
8. 确保返回的是有效的JSON格式
9. 特别重要：如果字段内容包含<think>...</think>标签，请保留这些标签并翻译标签内的内容
10. 禁止在JSON外部生成任何<think>标签或思考过程，直接输出JSON

输出格式：直接输出翻译后的JSON，第一个字符必须是"{"，最后一个字符必须是"}"

/no_think"""

        return prompt

    def _get_field_type(self, field_name: str) -> str:
        """根据字段名确定翻译类型"""
        field_lower = field_name.lower()
        if 'problem' in field_lower:
            return "problem"
        elif 'solution' in field_lower or 'answer' in field_lower:
            return "solution"
        elif 'expected' in field_lower:
            return "answer"
        else:
            return "general"

    def merge_chunks(self, chunk_files: List[str], output_path: str) -> bool:
        """合并所有单条目文件"""
        try:
            logger.info(f"开始合并 {len(chunk_files)} 个单条目文件")

            merged_data = []

            with tqdm(total=len(chunk_files), desc="🔗 读取单条目文件", unit="文件", colour='cyan') as pbar:
                for i, chunk_path in enumerate(chunk_files):
                    chunk_name = os.path.basename(chunk_path)
                    pbar.set_description(f"🔗 读取文件: {chunk_name}")

                    with open(chunk_path, 'r', encoding='utf-8') as f:
                        item = json.load(f)  # 读取单个条目

                    merged_data.append(item)  # 添加到列表
                    pbar.update(1)

                    row_number = item.get('row_number', i)
                    pbar.set_postfix({
                        "row_number": row_number,
                        "已读取": f"{i+1}/{len(chunk_files)}"
                    })

            # 按row_number排序（如果存在）
            try:
                merged_data.sort(key=lambda x: x.get('row_number', 0))
                logger.info("已按row_number排序")
            except Exception as e:
                logger.warning(f"排序失败，保持原顺序: {e}")

            # 保存合并数据
            logger.info("🔄 正在保存合并数据...")
            with tqdm(total=1, desc="🔄 保存数据", unit="步骤", colour='magenta') as merge_pbar:
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(merged_data, f, ensure_ascii=False, indent=2)
                merge_pbar.update(1)
                merge_pbar.set_postfix({"最终条目数": len(merged_data)})

            # 获取文件大小
            file_size = os.path.getsize(output_path) / 1024 / 1024

            logger.info(f"合并完成!")
            logger.info(f"输出文件: {output_path}")
            logger.info(f"文件大小: {file_size:.2f} MB")
            logger.info(f"总条目数: {len(merged_data)}")

            return True

        except Exception as e:
            logger.error(f"合并文件失败: {e}")
            return False

    def translate_in_chunks(self, fields_to_translate: List[str], output_path: str) -> bool:
        """
        分块翻译主方法

        Args:
            fields_to_translate: 需要翻译的字段名列表
            output_path: 输出文件路径

        Returns:
            翻译是否成功
        """
        if self.data is None:
            logger.error("请先加载JSON文件")
            return False

        # 第一步：检查是否已有块文件
        existing_chunks = self.get_existing_chunks()

        if existing_chunks:
            logger.info(f"发现 {len(existing_chunks)} 个已存在的单条目文件")
            user_choice = input("是否使用已存在的文件继续翻译？(y/n): ")
            if user_choice.lower() == 'y':
                chunk_files = existing_chunks
            else:
                logger.info("重新分割文件")
                self.cleanup_chunks()
                chunk_files = self.split_into_chunks()
        else:
            # 第一步：按row_number分割文件
            chunk_files = self.split_into_chunks()

        if not chunk_files:
            logger.error("文件分割失败")
            return False

        # 第二步：计算总任务数并开始翻译
        logger.info(f"开始翻译 {len(chunk_files)} 个自动分块文件")

        # 计算总翻译任务数（现在每个文件是一个任务）
        total_translation_tasks = len(chunk_files)  # 每个文件一个API调用

        self.total_tasks = total_translation_tasks
        self.start_time = time.time()

        failed_chunks = []

        # 显示翻译统计信息
        logger.info(f"按row_number翻译统计:")
        logger.info(f"- 文件数量: {len(chunk_files)}")
        logger.info(f"- 总翻译任务: {total_translation_tasks} 个文件")
        logger.info(f"- 并发限制: 8 个文件同时处理")

        # 使用ThreadPoolExecutor限制并发 (8B模型优化配置)
        max_workers = 8
        completed_count = 0

        # 创建总体进度条
        with tqdm(total=total_translation_tasks, desc="🌍 文件翻译进度", unit="文件",
                 colour='green') as overall_pbar:

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有翻译任务
                future_to_path = {
                    executor.submit(self.translate_chunk_file, chunk_path, fields_to_translate, None): chunk_path
                    for chunk_path in chunk_files
                }

                # 收集结果
                for future in as_completed(future_to_path):
                    chunk_path = future_to_path[future]
                    chunk_name = os.path.basename(chunk_path)

                    try:
                        success = future.result()
                        if not success:
                            failed_chunks.append(chunk_path)
                    except Exception as e:
                        logger.error(f"处理文件 {chunk_name} 时发生异常: {e}")
                        failed_chunks.append(chunk_path)

                    completed_count += 1
                    overall_pbar.update(1)

                    # 计算并显示统计信息
                    elapsed_time = time.time() - self.start_time
                    if completed_count > 0:
                        speed = completed_count / elapsed_time
                        remaining_tasks = total_translation_tasks - completed_count
                        eta_seconds = remaining_tasks / speed if speed > 0 else 0
                        eta_hours = eta_seconds / 3600

                        overall_pbar.set_postfix({
                            "速度": f"{speed:.1f}文件/秒",
                            "剩余": f"{eta_hours:.1f}h",
                            "失败": len(failed_chunks),
                            "成功率": f"{((completed_count - len(failed_chunks)) / completed_count * 100):.1f}%"
                        })

        # 处理失败的文件
        if failed_chunks:
            logger.warning(f"有 {len(failed_chunks)} 个文件翻译失败")
            user_choice = input("是否重试失败的文件？(y/n): ")
            if user_choice.lower() == 'y':
                logger.info("重试失败的文件...")
                retry_failed = []

                # 重试任务数就是失败文件数
                retry_tasks = len(failed_chunks)

                # 重试进度条
                with tqdm(total=retry_tasks, desc="🔄 重试失败文件", unit="文件", colour='red') as retry_pbar:
                    with ThreadPoolExecutor(max_workers=max_workers) as retry_executor:
                        # 提交重试任务
                        retry_future_to_path = {
                            retry_executor.submit(self.translate_chunk_file, chunk_path, fields_to_translate, None): chunk_path
                            for chunk_path in failed_chunks
                        }

                        # 收集重试结果
                        for future in as_completed(retry_future_to_path):
                            chunk_path = retry_future_to_path[future]
                            try:
                                success = future.result()
                                if not success:
                                    retry_failed.append(chunk_path)
                            except Exception as e:
                                logger.error(f"重试文件 {chunk_path} 时发生异常: {e}")
                                retry_failed.append(chunk_path)

                            retry_pbar.update(1)

                if retry_failed:
                    logger.error(f"仍有 {len(retry_failed)} 个文件翻译失败，将跳过这些文件")
                    failed_chunks = retry_failed
                else:
                    logger.info("所有文件重试成功")
                    failed_chunks = []

        # 第三步：合并块文件
        if failed_chunks:
            logger.warning("由于有失败的块，将跳过失败的块进行合并")
            successful_chunks = [chunk for chunk in chunk_files if chunk not in failed_chunks]
        else:
            successful_chunks = chunk_files

        success = self.merge_chunks(successful_chunks, output_path)

        if success:
            logger.info("翻译任务完成！")

            # 询问是否清理临时文件
            user_choice = input("是否清理临时块文件？(y/n): ")
            if user_choice.lower() == 'y':
                self.cleanup_chunks()

        return success


def main():
    """主函数"""
    # 配置进度条
    setup_progress_bars()

    # 配置参数
    json_file_path = "/home/<USER>/WorkSpace/notebooks/datasets/OpenMathReasoning-mini_cn/data/cot-00000-of-00001.json"
    output_file_path = "/home/<USER>/WorkSpace/notebooks/datasets/OpenMathReasoning-mini_cn/data/cot-00000-of-00001_translated.json"

    # 需要翻译的字段
    fields_to_translate = [
        "problem",           # 问题
        "generated_solution", # 生成的解决方案
        "expected_answer",   # 期望答案
    ]

    logger.info("开始JSON文件分块翻译任务")
    logger.info("=" * 60)

    # 配置参数 (8B模型优化配置)
    max_concurrent = 10   # VLLM内部并发限制 (8B模型稍微保守)
    max_file_workers = 8  # 同时处理的文件数

    print(f"按row_number分块翻译配置:")
    print(f"- VLLM并发限制: {max_concurrent}")
    print(f"- 文件处理并发: {max_file_workers} 个文件同时处理")
    print(f"- 分块方式: 每个row_number一个文件")
    print(f"- 处理方式: 按row_number分块 -> 一次API调用翻译整个文件 -> 合并")
    print(f"- 翻译字段: {fields_to_translate}")

    # 初始化翻译器
    translator = VLLMTranslator(
        model_name="/home/<USER>/Model/LLM/Qwen/Qwen3-8B-AWQ",
        max_concurrent=max_concurrent
    )

    # 检查VLLM连接
    logger.info("🔍 正在检查VLLM服务状态...")

    if not translator.check_vllm_connection():
        logger.error("❌ 无法连接到VLLM服务")
        logger.error("\n🛠️ 请检查以下项目:")
        logger.error("1. VLLM服务是否正在运行在8666端口")
        logger.error("2. Qwen3-8B-AWQ模型是否已正确加载")
        logger.error("3. 服务地址是否正确 (http://localhost:8666)")
        logger.error("4. 服务器资源是否充足 (GPU/CPU/内存)")
        logger.error("5. 是否有其他进程占用端口")
        return
    else:
        logger.info("✅ VLLM服务连接正常")

    # 初始化JSON处理器（按row_number分块）
    processor = JsonTranslator(json_file_path, translator)

    # 加载数据
    if not processor.load_json():
        return

    # 备份原始文件
    processor.backup_original_file()

    # 显示数据信息
    total_items = len(processor.data)
    total_files = total_items  # 每个条目一个文件

    # 计算预估时间（每个文件一次API调用，考虑并发效率）
    estimated_seconds_per_file = 5  # 每个文件预估5秒（包含多个字段的翻译）
    estimated_hours = (total_files * estimated_seconds_per_file) / (max_file_workers * 3600)

    print(f"\n按row_number分块翻译任务概览:")
    print(f"- 总条目数: {total_items}")
    print(f"- 翻译字段数: {len(fields_to_translate)}")
    print(f"- 文件数量: {total_files} 个（每个row_number一个文件）")
    print(f"- 处理方式: 每个文件一次API调用")
    print(f"- 文件并发数: {max_file_workers}")
    print(f"- 预估时间: {estimated_hours:.1f} 小时")
    print(f"- 支持断点续存: 是")
    print(f"- 自动备份: 是")
    print(f"- 内存优化: 是")

    # 询问用户确认
    user_input = input(f"\n是否开始按row_number分块翻译？(y/n): ")
    if user_input.lower() != 'y':
        logger.info("用户取消翻译任务")
        return

    # 开始按row_number分块翻译
    start_time = time.time()
    success = processor.translate_in_chunks(fields_to_translate, output_file_path)

    if success:
        # 显示统计信息
        end_time = time.time()
        duration = end_time - start_time

        # 获取实际生成的文件数
        actual_files = len(processor.get_existing_chunks())

        logger.info(f"\n按row_number分块翻译完成!")
        logger.info(f"总耗时: {duration:.2f} 秒 ({duration/3600:.2f} 小时)")
        logger.info(f"处理条目数: {total_items}")
        logger.info(f"翻译字段数: {len(fields_to_translate)}")
        logger.info(f"生成文件数: {actual_files}")
        logger.info(f"平均速度: {total_files/duration:.2f} 文件/秒")

        # 显示翻译结果预览
        try:
            with open(output_file_path, 'r', encoding='utf-8') as f:
                final_data = json.load(f)

            logger.info("\n翻译结果预览:")
            if final_data:
                sample_item = final_data[0]
                for field in fields_to_translate:
                    if field in sample_item:
                        translated = sample_item[field]
                        logger.info(f"\n{field} (翻译后):")
                        logger.info(f"{str(translated)[:200]}...")
        except Exception as e:
            logger.error(f"读取最终结果失败: {e}")
    else:
        logger.error("按row_number分块翻译失败")

    logger.info("任务完成!")

if __name__ == "__main__":
    main()