# Parquet文件翻译脚本使用说明

## 功能特性

### 🚀 核心功能
- **分块处理**: 将大文件分割成小块，内存友好，支持超大文件
- **并发翻译**: 支持多线程并发翻译，大幅提升效率
- **智能断点续存**: 块级别的断点续存，可跳过已处理的块
- **数学公式保留**: 完美保留LaTeX数学公式和符号
- **多层进度显示**: 分块进度、翻译进度、合并进度
- **自动备份**: 覆盖原文件前自动创建备份
- **容错处理**: 单块失败不影响其他块，支持重试
- **内存优化**: 分块处理避免大文件内存溢出

### 🛠 技术特性
- **VLLM集成**: 使用本地VLLM服务进行翻译
- **思考模式禁用**: 确保输出纯净的翻译结果
- **多格式输出**: 同时生成parquet和CSV格式
- **状态持久化**: 翻译状态自动保存到JSON文件

## 安装依赖

手动安装所需依赖包：
```bash
pip install pandas pyarrow requests tqdm
```

## 使用方法

### 1. 启动VLLM服务
确保你的VLLM服务正在运行：
```bash
vllm serve /home/<USER>/Model/LLM/Qwen/Qwen3-8B-AWQ \
  --gpu-memory-utilization 0.8 \
  --quantization awq \
  --tensor-parallel-size 4 \
  --port 8666
```

### 2. 开始翻译
```bash
# 开始翻译任务
python3 parquet_FY.py
```

## 翻译流程

### 分块处理流程
1. **连接检查**: 自动检测VLLM服务连接
2. **数据加载**: 加载原始parquet文件到内存
3. **文件分块**: 将大文件按行数分割成小块文件
4. **并发翻译**: 逐个处理每个块文件，支持并发翻译
5. **文件合并**: 将所有翻译后的块文件合并成最终文件
6. **清理临时**: 可选择清理临时块文件

### 智能断点续存
如果翻译过程中断：
1. **块文件检测**: 自动检测已存在的块文件
2. **用户选择**: 询问是否使用已有块文件继续
3. **跳过已完成**: 自动跳过已翻译完成的块
4. **重试失败**: 支持重试翻译失败的块

## 文件结构

### 输入文件
```
/home/<USER>/WorkSpace/notebooks/datasets/OpenMathReasoning-mini_cn/data/cot-00000-of-00001.parquet
```

### 输出文件
```
# 主要输出（覆盖原文件）
cot-00000-of-00001.parquet

# 备份文件
cot-00000-of-00001_backup.parquet

# CSV查看文件
cot-00000-of-00001_translated.csv

# 临时块文件目录
translation_chunks/
├── chunk_0000_0_999.parquet         # 第1块 (0-999行)
├── chunk_0001_1000_1999.parquet     # 第2块 (1000-1999行)
├── chunk_0002_2000_2999.parquet     # 第3块 (2000-2999行)
└── ...

# 进度状态目录
translation_progress/
└── translation_state.json          # 翻译状态
```

## 翻译配置

### 默认翻译列
- `problem`: 数学问题
- `generated_solution`: 解答过程

### 新增列
- `problem_cn`: 问题的中文翻译
- `generated_solution_cn`: 解答的中文翻译

### 性能参数
- **并发线程**: 10个
- **块大小**: 1000行/块
- **分块处理**: 自动分割大文件
- **断点续存**: 块级别断点续存
- **内存优化**: 逐块处理，避免内存溢出

## 预估时间

### 数据量
- **总行数**: 19,252行
- **翻译列数**: 2列
- **总任务数**: 38,504条

### 时间预估
- **单条翻译**: 2-5秒
- **串行总时间**: 21-54小时
- **并发翻译**: 10线程并发，提速5.3倍
- **并发总时间**: 3.2小时
- **实际可能更快**: 取决于网络和模型响应速度

## 分块并发优化

### 分块配置
- **默认并发数**: 10个线程
- **块大小**: 1000行/块
- **连接复用**: 自动复用HTTP连接
- **智能重试**: 失败自动重试，指数退避

### 分块优势
- **内存友好**: 避免大文件一次性加载
- **容错性强**: 单块失败不影响其他块
- **断点续存**: 块级别的精确断点续存
- **并行处理**: 可同时处理多个块（未来扩展）

### 性能提升
- **理论提速**: 10倍（10个并发线程）
- **实际提速**: 5.3倍（考虑网络延迟和资源竞争）
- **内存使用**: 大幅降低（分块处理）
- **GPU使用**: 更充分利用模型并发能力

### 性能调优建议
1. **并发数调整**: 根据GPU内存和网络带宽调整
2. **块大小调整**: 较大块减少IO开销，较小块提高容错性
3. **监控资源**: 观察GPU利用率和内存使用
4. **网络优化**: 确保网络带宽充足

## 故障排除

### 常见问题

#### 1. VLLM连接失败
```
错误: 无法连接到VLLM服务
解决: 检查VLLM服务是否在8666端口运行
```

#### 2. 模型路径错误
```
错误: 模型不可用
解决: 确认模型路径 /home/<USER>/Model/LLM/Qwen/Qwen3-8B-AWQ 正确
```

#### 3. 内存不足
```
错误: CUDA out of memory
解决: 降低 --gpu-memory-utilization 参数
```

#### 4. 翻译质量问题
```
问题: 输出包含思考过程
解决: 确认 enable_thinking: False 参数生效
```

### 恢复操作

#### 从备份恢复
```bash
# 如果翻译出错，从备份恢复原文件
cp cot-00000-of-00001_backup.parquet cot-00000-of-00001.parquet
```

#### 清除进度重新开始
```bash
# 删除所有临时文件，重新开始翻译
rm -rf translation_chunks/
rm -rf translation_progress/
```

#### 清理块文件
```bash
# 只清理块文件，保留进度状态
rm -rf translation_chunks/
```

## 监控和日志

### 实时监控
- **分块进度条**: 显示文件分割进度
- **翻译进度条**: 显示块文件翻译进度
- **合并进度条**: 显示文件合并进度
- **详细日志**: 每个步骤的详细操作日志

### 状态文件
```json
{
  "current_column": "problem",
  "current_row": 1250,
  "total_rows": 19252,
  "timestamp": 1703123456.789,
  "completed_columns": []
}
```

## 注意事项

1. **长时间运行**: 完整翻译需要很长时间，建议在稳定环境中运行
2. **网络稳定**: 确保网络连接稳定，避免频繁中断
3. **磁盘空间**: 确保有足够磁盘空间存储备份和进度文件
4. **资源监控**: 监控GPU和内存使用情况
5. **定期检查**: 定期检查翻译质量和进度

## 高级用法

### 自定义翻译列
修改 `parquet_FY.py` 中的 `columns_to_translate` 列表：
```python
columns_to_translate = [
    "problem",
    "generated_solution",
    # "expected_answer",  # 可以添加其他列
]
```

### 调整性能参数
```python
# 修改块大小和并发数
processor = ParquetTranslator(
    parquet_file_path,
    translator,
    chunk_size=2000  # 增大块大小
)

# 修改并发数
translator = VLLMTranslator(
    model_name="/home/<USER>/Model/LLM/Qwen/Qwen3-8B-AWQ",
    max_concurrent=15  # 增加并发数
)
```
