{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### News"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Placeholder"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Placeholder"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 371, "referenced_widgets": ["c95f8f778a9844debb61f5d29bafc0eb", "f307f833808141b1ab4e3f2d498402d4", "7090b566c853469abe05448b5394a718", "81e19ef5f8ac42cf8a0faaf3814a9fbb", "b255ae12c7f445409a9d4cc6c5b44c03", "a10464b4688b4107a8829f2140a4d3d9", "7fbe04b431f64106a9a168deb13fdd47", "59f41e47f54c4d74b963c76361d17b24", "37d84aac6794488f9e573e5ef3ee7eb6", "0ae9d22e24664a44b318008a1f0dbb38", "d6a2fef5b01b4cbe91632a315a0203de", "c4f21f744c9d41f9ae0f5c7126dc983d", "7ef2a9f4439347b5b00cbded2f49dbcb", "ed0863b1a49b4ed3a58e52185449a8d0", "2b82ee66f1c3411ab727a09be83c3c5a", "1b3a3c76a4db4f51a25cd2515bf11c69", "91c6b2c7a75b45dd9800a5e01718da15", "ddb30a3d95504080aa3157ba2d7a5cd7", "29303e7ab9f444f4a3b7372f17b9f9bc", "2a408a0e8df142cf8d953ba33cb3fb5e", "4210cdc5f8b44a60961261f45a782269", "af7b3740d81a4cd1a5d7ce3c06239a3d", "df6173f6ab4b447aab5ce8a00d5e1e69", "60d0e2a6bc2c4615b9c49a995c3aa1ad", "740e9ac282de489997a6da2fd4dd280e", "fbd87205bc6040069c1e1cbbabc1eac0", "0ea6a30a030747338d0d269a5964154e", "0f33c83f72a644d197e0d22aba5354d7", "222e6815f0904ed4a861235b0d699794", "689432885aa14636bbbbfde2ba2f039b", "8dd3a708204d496aa458013bcd1cbe07", "5683d080de874033b14d9720799c3032", "fa0feeb82a6646dcb2bd784687b7d95d", "065928bbb1844d828f752d9aeec4d438", "8cd62b09be894f6d83ebb9122052f7b0", "9cb62d4137254277a8245f67254364a7", "e2d233d08d354060a15322013ba67c00", "88373dd83bff487fbf3f933d0aa4ec82", "74e2098b2abc4511980e7cc8389fdea3", "20855c80476e4276ac89a1de038629a0", "79073b3e86794ab29b99310f1186a22a", "d716494305354dce9bcd8c7c334767d1", "006d2e9396414270ba092f0117b38399", "dbdb211edb83430392608c9480fad2b5", "5452a280b4554784a9517e62bdd04714", "1bbc105c29d440beb80594727cf1c2e8", "ce6c757ff6b54cb3aa717e405f4a9ecc", "cf18e9aa457f43b4997f59d1c6db6d4f", "40122d0b9fc448e2bc2e793040ae8614", "aef0c23ef24d4f79ab5ccc6d36a65043", "deaefdc2e5d441268cbee1775e1f712a", "ea4918431f6843199553b050c1fb7f8c", "cd13e7bf9c3a440aafff0e7b8152acf2", "b8563a58fa644d4a9b90b93096e675df", "cc69aadfcb99497e865c9c81c0f4b48d", "814512fef9164353b858bd62e2f562c6", "62664c70849c4efaa650e70ea91f2ac3", "236c3af33e97493ead42e43b95f2e73b", "8ba5519a325a46dba5e4c973903a8729", "f7b370463938455ea3da9c7c5629b873", "8eeb5fbc4bef4f81a0189a938b0df88e", "afbcd15b5e2f42b88be4bba8af63a712", "7771d92cc04747abafb8689e017508f1", "4c821ee18e6c45c5aedd826fd7b39209", "174616a5de704ac5a7ff310be7de4c47", "408004b178e94b4b8e279c58b7e2bd2e"]}, "id": "2eSvM9zX_2d3", "outputId": "d0b6c66e-ab67-4241-bdf1-b1babc18c2d6"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c95f8f778a9844debb61f5d29bafc0eb", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/1.14k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["==((====))==  Unsloth: Fast Llama patching release 2024.4\n", "   \\\\   /|    GPU: Tesla T4. Max memory: 14.748 GB. Platform = Linux.\n", "O^O/ \\_/ \\    Pytorch: 2.2.1+cu121. CUDA = 7.5. CUDA Toolkit = 12.1.\n", "\\        /    Bfloat16 = FALSE. Xformers = 0.0.25.post1. FA = False.\n", " \"-____-\"     Free Apache license: http://github.com/unslothai/unsloth\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Unused kwargs: ['_load_in_4bit', '_load_in_8bit', 'quant_method']. These kwargs are not used in <class 'transformers.utils.quantization_config.BitsAndBytesConfig'>.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c4f21f744c9d41f9ae0f5c7126dc983d", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/5.70G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "df6173f6ab4b447aab5ce8a00d5e1e69", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/131 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "065928bbb1844d828f752d9aeec4d438", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/50.6k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5452a280b4554784a9517e62bdd04714", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/9.09M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "814512fef9164353b858bd62e2f562c6", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/449 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.\n", "Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.\n"]}], "source": ["from unsloth import FastLanguageModel\n", "import torch\n", "max_seq_length = 4096 # Choose any! We auto support RoPE Scaling internally!\n", "dtype = None # None for auto detection. Float16 for Tesla T4, V100, Bfloat16 for Ampere+\n", "load_in_4bit = True # Use 4bit quantization to reduce memory usage. Can be False.\n", "\n", "# 4bit pre quantized models we support for 4x faster downloading + no OOMs.\n", "fourbit_models = [\n", "    \"unsloth/mistral-7b-bnb-4bit\",\n", "    \"unsloth/mistral-7b-instruct-v0.2-bnb-4bit\",\n", "    \"unsloth/llama-2-7b-bnb-4bit\",\n", "    \"unsloth/gemma-7b-bnb-4bit\",\n", "    \"unsloth/gemma-7b-it-bnb-4bit\", # Instruct version of Gemma 7b\n", "    \"unsloth/gemma-2b-bnb-4bit\",\n", "    \"unsloth/gemma-2b-it-bnb-4bit\", # Instruct version of Gemma 2b\n", "    \"unsloth/llama-3-8b-bnb-4bit\", # [NEW] 15 Trillion token Llama-3\n", "] # More models at https://huggingface.co/unsloth\n", "\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name = \"unsloth/llama-3-8b-bnb-4bit\",\n", "    max_seq_length = max_seq_length,\n", "    dtype = dtype,\n", "    load_in_4bit = load_in_4bit,\n", "    # token = \"hf_...\", # use one if using gated models like meta-llama/Llama-2-7b-hf\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "_H6EWbe-t_bk"}, "source": ["We now add LoRA adapters so we only need to update 1 to 10% of all parameters!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6bZsfBuZDeCL", "outputId": "34834b73-2386-45f5-d197-7f610106a053"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unsloth 2024.4 patched 32 layers with 32 QKV layers, 32 O layers and 32 MLP layers.\n"]}], "source": ["model = FastLanguageModel.get_peft_model(\n", "    model,\n", "    r = 16, # Choose any number > 0 ! Suggested 8, 16, 32, 64, 128\n", "    target_modules = [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                      \"gate_proj\", \"up_proj\", \"down_proj\",],\n", "    lora_alpha = 16,\n", "    lora_dropout = 0, # Supports any, but = 0 is optimized\n", "    bias = \"none\",    # Supports any, but = \"none\" is optimized\n", "    # [NEW] \"unsloth\" uses 30% less VRAM, fits 2x larger batch sizes!\n", "    use_gradient_checkpointing = \"unsloth\", # True or \"unsloth\" for very long context\n", "    random_state = 3407,\n", "    use_rslora = False,  # We support rank stabilized LoRA\n", "    loftq_config = None, # And LoftQ\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "FNfc6OLOuuyZ"}, "source": ["<a name=\"Data\"></a>\n", "### Data Prep\n", "We now use a special ORPO style dataset from [recipe-research](https://huggingface.co/datasets/reciperesearch/dolphin-sft-v0.1-preference).\n", "\n", "You need at least 3 columns:\n", "* Instruction\n", "* Accepted\n", "* Rejected\n", "\n", "For example:\n", "* Instruction: \"What is 2+2?\"\n", "* Accepted: \"The answer is 4\"\n", "* Rejected: \"The answer is 5\"\n", "\n", "The goal of ORPO is to penalize the \"rejected\" samples, and increase the likelihood of \"accepted\" samples. [recipe-research](https://huggingface.co/datasets/reciperesearch/dolphin-sft-v0.1-preference) essentially used Mistral to generate the \"rejected\" responses, and used GPT-4 to generated the \"accepted\" responses."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 145, "referenced_widgets": ["4b617d72867444ca9da6ab3570a3eac8", "7cab13ec19f344e4b549e11e354aa72a", "5d3d72f6e9254406a3e97aa250b86aa4", "83c3d3b719a84fabb73ff8f965acf2e3", "14b54e5181514e908be0e74d65d8fe8a", "bc4dbff595784744aa6fdf3d8293455e", "0217a84114504cc2a2d68a0d75b093ef", "68e05c5302b3431b811b26c8d8db3903", "26b3e7f04643427996105bcce54480bc", "97b041295cd0499f90678a30887bd17d", "06906e6f5a804f568382f50ba6bead84", "13a59e79b8c1400aabffe376477223b9", "2b4cb8cc837346a89e5558579f7e89eb", "2f11689c28274c3687ae6654152a7ae6", "3e930eaedc1b468090be01a77b3e71be", "d39c3584f185426fbc29458e2121a1db", "1fef5b3b0e3a49a98690087c371a238f", "37abe3080ffd4c2190aa387c17bde383", "f3cf4c7b080844d8972c62d920cd6f12", "eb83ea8a549c414abf26567c951632f4", "33c7551358bf42c3a5a3e0d987377a54", "dddcec9e675f42fe895ccb315dbbf6b7", "f43beff2d5e742e28208a19bd326ce39", "7b8d69f00f964491b78857c918723d5a", "de4a4eb3a8464d59abcd85ab923d63ff", "07f607466207436286302004aca8b9c8", "35ee77b4c52e4d61b3a1779e2826ef93", "d447f6c5960b46b9b86e1b2c82ba211a", "c96d2a6a7b364996b3882d08abaf5be6", "9b942981c5ce4f3aa05ded98af90828b", "784302c339174535a5b16eef09331d9d", "156621bd27dc4fffa3928bca9dac7e25", "7de44361a92d498b8aeec9876020a9be", "3be775e3950349afac0d1677db27d36d", "6f4db5b0ce954161a5da5ad53457286c", "4291b6a90b824776bd3115b892de4f2e", "5976c94a9c0449c480e9e91faf039724", "b4461b01dcb04063a4cbdcafe7b06a71", "d9eceda806d44cd5b10a4aa2c822a189", "d74bb432b30e4a96ac8719364a4fa093", "fe131f004161495f8729bcbd4663c50c", "0bf68aa188364c5e8516bd9658ef4987", "ad28db7e853f48bdba90d2da5cf26d24", "0bda54a16c44400ab266e18abb7395af"]}, "id": "GetrTh37qgDp", "outputId": "ab229333-42b5-45d7-fa01-f474622ae962"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4b617d72867444ca9da6ab3570a3eac8", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading readme:   0%|          | 0.00/490 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "13a59e79b8c1400aabffe376477223b9", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading data:   0%|          | 0.00/34.1M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f43beff2d5e742e28208a19bd326ce39", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating train split:   0%|          | 0/16000 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3be775e3950349afac0d1677db27d36d", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/16000 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# The data must be formatted with appropriate prompt template first.\n", "# See details here: https://github.com/huggingface/trl/blob/main/examples/scripts/orpo.py\n", "\n", "alpaca_prompt = \"\"\"Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\n", "\n", "### Instruction:\n", "{}\n", "\n", "### Input:\n", "{}\n", "\n", "### Response:\n", "{}\"\"\"\n", "\n", "EOS_TOKEN = tokenizer.eos_token # Must add EOS_TOKEN\n", "\n", "def format_prompt(sample):\n", "    instruction = sample[\"instruction\"]\n", "    input       = sample[\"input\"]\n", "    accepted    = sample[\"accepted\"]\n", "    rejected    = sample[\"rejected\"]\n", "\n", "    # ORPOTrainer expects prompt/chosen/rejected keys\n", "    # See: https://huggingface.co/docs/trl/main/en/orpo_trainer\n", "    sample[\"prompt\"]   = alpaca_prompt.format(instruction, input, \"\")\n", "    sample[\"chosen\"]   = accepted + EOS_TOKEN\n", "    sample[\"rejected\"] = rejected + EOS_TOKEN\n", "    return sample\n", "pass\n", "\n", "from datasets import load_dataset\n", "dataset = load_dataset(\"reciperesearch/dolphin-sft-v0.1-preference\")[\"train\"]\n", "dataset = dataset.map(format_prompt,)"]}, {"cell_type": "markdown", "metadata": {"id": "7PH-7Cudu8Ja"}, "source": ["Let's print out some examples to see how the dataset should look like"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "oF63zQqNlNJC", "outputId": "53cf0030-72b1-4654-9034-2317cdf122c7"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["INSTRUCTION: ==================================================\n", "('Below is an instruction that describes a task, paired with an input that '\n", " 'provides further context. Write a response that appropriately completes the '\n", " 'request.\\n'\n", " '\\n'\n", " '### Instruction:\\n'\n", " 'You are an AI assistant that helps people find information.\\n'\n", " '\\n'\n", " '### Input:\\n'\n", " 'Given the rationale, provide a reasonable question and answer. Step-by-step '\n", " 'reasoning process: Xkcd comics are very popular amongst internet users.\\n'\n", " ' The question and answer:\\n'\n", " '\\n'\n", " '### Response:\\n')\n", "ACCEPTED: ==================================================\n", "('Question: What makes Xkcd comics popular among internet users?\\n'\n", " '\\n'\n", " 'Answer: Xkcd comics are popular among internet users because of their clever '\n", " 'humor, relatable themes, and minimalist art style. They often cover topics '\n", " 'like science, technology, and life experiences, making them appealing to a '\n", " 'broad audience.<|end_of_text|>')\n", "REJECTED: ==================================================\n", "('Question: What is the reason behind the popularity of Xkcd comics among '\n", " 'internet users?\\n'\n", " '\\n'\n", " 'Answer: Xkcd comics are popular among internet users because they offer a '\n", " 'unique blend of humor, relatable content, and thought-provoking topics that '\n", " 'resonate with a wide range of people. The comics often address everyday '\n", " 'experiences, technology, and social issues, making them accessible and '\n", " 'enjoyable for many individuals. Additionally, the simple and minimalistic '\n", " 'art style of Xkcd comics allows for easy comprehension and sharing, '\n", " 'contributing to their widespread appeal.<|end_of_text|>')\n"]}], "source": ["import pprint\n", "\n", "row = dataset[1]\n", "print(\"INSTRUCTION: \" + \"=\" * 50)\n", "pprint.pprint(row[\"prompt\"])\n", "print(\"ACCEPTED: \" + \"=\" * 50)\n", "pprint.pprint(row[\"chosen\"])\n", "print(\"REJECTED: \" + \"=\" * 50)\n", "pprint.pprint(row[\"rejected\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "oqyleKojqgDq"}, "outputs": [], "source": ["# Enable reward modelling stats\n", "from unsloth import PatchDPOTrainer\n", "\n", "PatchDPOTrainer()"]}, {"cell_type": "markdown", "metadata": {"id": "J1ZlnJpkxIuV"}, "source": ["<a name=\"<PERSON>\"></a>\n", "### Train the model\n", "Now let's use Huggingface TRL's `ORPOTrainer`! More docs here: [TRL ORPO docs](https://huggingface.co/docs/trl/main/en/orpo_trainer). We do 60 steps to speed things up, but you can set `num_train_epochs=1` for a full run, and turn off `max_steps=None`. We also support TRL's `DPOTrainer`!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 122, "referenced_widgets": ["a11ecbe5a63a495683f37eeb9c59bf31", "e01a575bbfd84ccd9c6acb628632d848", "10150def49574b8c9697d69b3068df4a", "a48e289710e84e1192a8a85175e517a4", "fe0a86aa30134213a613bab9407a10f3", "6eb5b5bfae5140df89e32e7868ab4e98", "7f0eaebaebb944d99a291119dac94faf", "ca1bae6d505c4513bddb1666478adec8", "c2ee270cf91f4eee8354b2a056d997ce", "7cef798c923c471f87ddb35f7fa886b5", "f5872532493342b68457e165ca3bc255"]}, "id": "QtoqUw80QDV0", "outputId": "509a9b9f-dac1-4dd8-86b5-320af93a45ab"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/dist-packages/trl/trainer/orpo_trainer.py:247: UserWarning: When using DPODataCollatorWithPadding, you should set `remove_unused_columns=False` in your TrainingArguments we have set it for you, but you should do it yourself in the future.\n", "  warnings.warn(\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a11ecbe5a63a495683f37eeb9c59bf31", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/16000 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["max_steps is given, it will override any value given in num_train_epochs\n"]}], "source": ["from trl import ORPOConfig, ORPOTrainer\n", "from unsloth import is_bfloat16_supported\n", "\n", "orpo_trainer = ORPOTrainer(\n", "    model = model,\n", "    train_dataset = dataset,\n", "    tokenizer = tokenizer,\n", "    args = ORPOConfig(\n", "        max_length = max_seq_length,\n", "        max_prompt_length = max_seq_length//2,\n", "        max_completion_length = max_seq_length//2,\n", "        per_device_train_batch_size = 2,\n", "        gradient_accumulation_steps = 4,\n", "        beta = 0.1,\n", "        logging_steps = 1,\n", "        optim = \"adamw_8bit\",\n", "        lr_scheduler_type = \"linear\",\n", "        max_steps = 30, # Change to num_train_epochs = 1 for full training runs\n", "        fp16 = not is_bfloat16_supported(),\n", "        bf16 = is_bfloat16_supported(),\n", "        output_dir = \"outputs\",\n", "        report_to = \"none\", # Use this for WandB etc\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "EWGFqAo5Q2me", "outputId": "34e5a57e-fe2a-4b44-99fc-cdbccf669d70"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["==((====))==  Unsloth - 2x faster free finetuning | Num GPUs = 1\n", "   \\\\   /|    Num examples = 16,000 | Num Epochs = 1\n", "O^O/ \\_/ \\    Batch size per device = 2 | Gradient Accumulation steps = 4\n", "\\        /    Total batch size = 8 | Total steps = 30\n", " \"-____-\"     Number of trainable parameters = 41,943,040\n", "Could not estimate the number of tokens of the input, floating-point operations will not be computed\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='30' max='30' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [30/30 10:33, Epo<PERSON> 0/1]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "      <th>rewards / chosen</th>\n", "      <th>rewards / rejected</th>\n", "      <th>rewards / accuracies</th>\n", "      <th>rewards / margins</th>\n", "      <th>logps / rejected</th>\n", "      <th>logps / chosen</th>\n", "      <th>logits / rejected</th>\n", "      <th>logits / chosen</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>5.696200</td>\n", "      <td>-0.314192</td>\n", "      <td>-0.139184</td>\n", "      <td>0.000000</td>\n", "      <td>-0.175007</td>\n", "      <td>-1.391845</td>\n", "      <td>-3.141916</td>\n", "      <td>-0.577899</td>\n", "      <td>-0.558586</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>4.613900</td>\n", "      <td>-0.310306</td>\n", "      <td>-0.160126</td>\n", "      <td>0.125000</td>\n", "      <td>-0.150180</td>\n", "      <td>-1.601255</td>\n", "      <td>-3.103056</td>\n", "      <td>-0.939558</td>\n", "      <td>-0.772177</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>5.087600</td>\n", "      <td>-0.215214</td>\n", "      <td>-0.194589</td>\n", "      <td>0.250000</td>\n", "      <td>-0.020624</td>\n", "      <td>-1.945894</td>\n", "      <td>-2.152137</td>\n", "      <td>-0.750424</td>\n", "      <td>-0.748731</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>3.823400</td>\n", "      <td>-0.288235</td>\n", "      <td>-0.289640</td>\n", "      <td>0.250000</td>\n", "      <td>0.001405</td>\n", "      <td>-2.896399</td>\n", "      <td>-2.882351</td>\n", "      <td>-0.972239</td>\n", "      <td>-1.037048</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>3.636200</td>\n", "      <td>-0.138244</td>\n", "      <td>-0.123302</td>\n", "      <td>0.625000</td>\n", "      <td>-0.014942</td>\n", "      <td>-1.233017</td>\n", "      <td>-1.382437</td>\n", "      <td>-0.742390</td>\n", "      <td>-0.715153</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>2.950500</td>\n", "      <td>-0.226424</td>\n", "      <td>-0.291118</td>\n", "      <td>0.500000</td>\n", "      <td>0.064694</td>\n", "      <td>-2.911181</td>\n", "      <td>-2.264242</td>\n", "      <td>-0.931786</td>\n", "      <td>-0.948551</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>2.191200</td>\n", "      <td>-0.178283</td>\n", "      <td>-0.201864</td>\n", "      <td>0.500000</td>\n", "      <td>0.023581</td>\n", "      <td>-2.018638</td>\n", "      <td>-1.782828</td>\n", "      <td>-1.227170</td>\n", "      <td>-1.240975</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>2.057900</td>\n", "      <td>-0.139332</td>\n", "      <td>-0.110689</td>\n", "      <td>0.250000</td>\n", "      <td>-0.028643</td>\n", "      <td>-1.106888</td>\n", "      <td>-1.393321</td>\n", "      <td>-1.249107</td>\n", "      <td>-1.263795</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>2.147400</td>\n", "      <td>-0.134407</td>\n", "      <td>-0.099334</td>\n", "      <td>0.250000</td>\n", "      <td>-0.035072</td>\n", "      <td>-0.993341</td>\n", "      <td>-1.344065</td>\n", "      <td>-1.282116</td>\n", "      <td>-1.354808</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>1.837500</td>\n", "      <td>-0.212703</td>\n", "      <td>-0.186428</td>\n", "      <td>0.000000</td>\n", "      <td>-0.026275</td>\n", "      <td>-1.864279</td>\n", "      <td>-2.127032</td>\n", "      <td>-1.162032</td>\n", "      <td>-1.108897</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>1.933000</td>\n", "      <td>-0.156348</td>\n", "      <td>-0.097927</td>\n", "      <td>0.000000</td>\n", "      <td>-0.058421</td>\n", "      <td>-0.979267</td>\n", "      <td>-1.563482</td>\n", "      <td>-1.190825</td>\n", "      <td>-1.245409</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>1.973500</td>\n", "      <td>-0.200500</td>\n", "      <td>-0.159612</td>\n", "      <td>0.000000</td>\n", "      <td>-0.040889</td>\n", "      <td>-1.596117</td>\n", "      <td>-2.005002</td>\n", "      <td>-1.380260</td>\n", "      <td>-1.329627</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>1.634200</td>\n", "      <td>-0.203949</td>\n", "      <td>-0.153286</td>\n", "      <td>0.125000</td>\n", "      <td>-0.050662</td>\n", "      <td>-1.532863</td>\n", "      <td>-2.039488</td>\n", "      <td>-1.185556</td>\n", "      <td>-1.275752</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>1.876500</td>\n", "      <td>-0.127660</td>\n", "      <td>-0.088646</td>\n", "      <td>0.375000</td>\n", "      <td>-0.039013</td>\n", "      <td>-0.886463</td>\n", "      <td>-1.276598</td>\n", "      <td>-1.225720</td>\n", "      <td>-1.241006</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>1.584200</td>\n", "      <td>-0.146653</td>\n", "      <td>-0.123118</td>\n", "      <td>0.000000</td>\n", "      <td>-0.023535</td>\n", "      <td>-1.231185</td>\n", "      <td>-1.466535</td>\n", "      <td>-1.250568</td>\n", "      <td>-1.257678</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>1.752800</td>\n", "      <td>-0.135002</td>\n", "      <td>-0.080984</td>\n", "      <td>0.000000</td>\n", "      <td>-0.054018</td>\n", "      <td>-0.809841</td>\n", "      <td>-1.350021</td>\n", "      <td>-1.202422</td>\n", "      <td>-1.211266</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>1.627300</td>\n", "      <td>-0.099521</td>\n", "      <td>-0.067955</td>\n", "      <td>0.000000</td>\n", "      <td>-0.031566</td>\n", "      <td>-0.679553</td>\n", "      <td>-0.995214</td>\n", "      <td>-1.287499</td>\n", "      <td>-1.307789</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>1.800100</td>\n", "      <td>-0.123857</td>\n", "      <td>-0.085257</td>\n", "      <td>0.125000</td>\n", "      <td>-0.038600</td>\n", "      <td>-0.852571</td>\n", "      <td>-1.238573</td>\n", "      <td>-1.288435</td>\n", "      <td>-1.280339</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>1.776000</td>\n", "      <td>-0.107769</td>\n", "      <td>-0.076882</td>\n", "      <td>0.125000</td>\n", "      <td>-0.030888</td>\n", "      <td>-0.768817</td>\n", "      <td>-1.077694</td>\n", "      <td>-1.211875</td>\n", "      <td>-1.201562</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>1.480900</td>\n", "      <td>-0.113957</td>\n", "      <td>-0.082669</td>\n", "      <td>0.250000</td>\n", "      <td>-0.031288</td>\n", "      <td>-0.826692</td>\n", "      <td>-1.139575</td>\n", "      <td>-1.079398</td>\n", "      <td>-1.131909</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>1.593300</td>\n", "      <td>-0.088314</td>\n", "      <td>-0.054479</td>\n", "      <td>0.000000</td>\n", "      <td>-0.033835</td>\n", "      <td>-0.544790</td>\n", "      <td>-0.883143</td>\n", "      <td>-1.072568</td>\n", "      <td>-1.130443</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>1.754200</td>\n", "      <td>-0.103276</td>\n", "      <td>-0.097119</td>\n", "      <td>0.125000</td>\n", "      <td>-0.006157</td>\n", "      <td>-0.971189</td>\n", "      <td>-1.032762</td>\n", "      <td>-1.134647</td>\n", "      <td>-1.186859</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>1.376700</td>\n", "      <td>-0.096716</td>\n", "      <td>-0.079258</td>\n", "      <td>0.125000</td>\n", "      <td>-0.017458</td>\n", "      <td>-0.792582</td>\n", "      <td>-0.967161</td>\n", "      <td>-1.022214</td>\n", "      <td>-1.007320</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>1.642400</td>\n", "      <td>-0.106709</td>\n", "      <td>-0.072481</td>\n", "      <td>0.125000</td>\n", "      <td>-0.034228</td>\n", "      <td>-0.724806</td>\n", "      <td>-1.067085</td>\n", "      <td>-0.955501</td>\n", "      <td>-1.042999</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>1.424300</td>\n", "      <td>-0.135257</td>\n", "      <td>-0.081045</td>\n", "      <td>0.125000</td>\n", "      <td>-0.054212</td>\n", "      <td>-0.810449</td>\n", "      <td>-1.352572</td>\n", "      <td>-1.015064</td>\n", "      <td>-1.071248</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>1.365000</td>\n", "      <td>-0.109444</td>\n", "      <td>-0.079274</td>\n", "      <td>0.125000</td>\n", "      <td>-0.030171</td>\n", "      <td>-0.792736</td>\n", "      <td>-1.094441</td>\n", "      <td>-1.103141</td>\n", "      <td>-1.140949</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>1.529900</td>\n", "      <td>-0.126537</td>\n", "      <td>-0.088402</td>\n", "      <td>0.250000</td>\n", "      <td>-0.038135</td>\n", "      <td>-0.884018</td>\n", "      <td>-1.265368</td>\n", "      <td>-0.991220</td>\n", "      <td>-1.012082</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>1.388800</td>\n", "      <td>-0.101727</td>\n", "      <td>-0.065627</td>\n", "      <td>0.000000</td>\n", "      <td>-0.036099</td>\n", "      <td>-0.656274</td>\n", "      <td>-1.017269</td>\n", "      <td>-0.894547</td>\n", "      <td>-0.934883</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>1.828400</td>\n", "      <td>-0.115963</td>\n", "      <td>-0.076427</td>\n", "      <td>0.000000</td>\n", "      <td>-0.039536</td>\n", "      <td>-0.764271</td>\n", "      <td>-1.159630</td>\n", "      <td>-1.092558</td>\n", "      <td>-1.179406</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>1.660200</td>\n", "      <td>-0.144630</td>\n", "      <td>-0.061597</td>\n", "      <td>0.000000</td>\n", "      <td>-0.083033</td>\n", "      <td>-0.615965</td>\n", "      <td>-1.446295</td>\n", "      <td>-0.883829</td>\n", "      <td>-1.028757</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["TrainOutput(global_step=30, training_loss=2.234786526362101, metrics={'train_runtime': 665.6376, 'train_samples_per_second': 0.361, 'train_steps_per_second': 0.045, 'total_flos': 0.0, 'train_loss': 2.234786526362101, 'epoch': 0.015})"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["orpo_trainer.train()"]}, {"cell_type": "markdown", "metadata": {"id": "FgEvCW76xblp"}, "source": ["<a name=\"Inference\"></a>\n", "### Inference\n", "Let's run the model! You can change the instruction and input - leave the output blank!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "0DJPbbtGxcFJ", "outputId": "6b25cdda-c86f-4f25-99e7-90c170d97197"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Setting `pad_token_id` to `eos_token_id`:128001 for open-end generation.\n"]}, {"data": {"text/plain": ["['<|begin_of_text|>Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\\n\\n### Instruction:\\nContinue the fi<PERSON><PERSON><PERSON> sequence.\\n\\n### Input:\\n1, 1, 2, 3, 5, 8\\n\\n### Response:\\n13<|end_of_text|>']"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# alpaca_prompt = Copied from above\n", "FastLanguageModel.for_inference(model)  # Enable native 2x faster inference\n", "inputs = tokenizer(\n", "    [\n", "        alpaca_prompt.format(\n", "            \"Continue the fi<PERSON><PERSON><PERSON> sequence.\",  # instruction\n", "            \"1, 1, 2, 3, 5, 8\",  # input\n", "            \"\",  # output - leave this blank for generation!\n", "        )\n", "    ],\n", "    return_tensors=\"pt\",\n", ").to(\"cuda\")\n", "\n", "outputs = model.generate(**inputs, max_new_tokens=64, use_cache=True)\n", "tokenizer.batch_decode(outputs)"]}, {"cell_type": "markdown", "metadata": {"id": "absTV8M1xzwz"}, "source": [" You can also use a `TextStreamer` for continuous inference - so you can see the generation token by token, instead of waiting the whole time!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xKzWRN1Px0Bg", "outputId": "fcbe0342-a227-4a1d-be7b-039f37193ec3"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Setting `pad_token_id` to `eos_token_id`:128001 for open-end generation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<|begin_of_text|>Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\n", "\n", "### Instruction:\n", "Continue the fi<PERSON><PERSON><PERSON> sequence.\n", "\n", "### Input:\n", "1, 1, 2, 3, 5, 8\n", "\n", "### Response:\n", "13<|end_of_text|>\n"]}], "source": ["# alpaca_prompt = Copied from above\n", "FastLanguageModel.for_inference(model)  # Enable native 2x faster inference\n", "inputs = tokenizer(\n", "    [\n", "        alpaca_prompt.format(\n", "            \"Continue the fi<PERSON><PERSON><PERSON> sequence.\",  # instruction\n", "            \"1, 1, 2, 3, 5, 8\",  # input\n", "            \"\",  # output - leave this blank for generation!\n", "        )\n", "    ],\n", "    return_tensors=\"pt\",\n", ").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "\n", "text_streamer = TextStreamer(tokenizer)\n", "_ = model.generate(**inputs, streamer=text_streamer, max_new_tokens=128)"]}, {"cell_type": "markdown", "metadata": {"id": "Y_3rdZXmx3Hh"}, "source": ["<a name=\"Save\"></a>\n", "### Saving, loading finetuned models\n", "To save the final model as LoRA adapters, either use Huggingface's `push_to_hub` for an online save or `save_pretrained` for a local save.\n", "\n", "**[NOTE]** This ONLY saves the LoRA adapters, and not the full model. To save to 16bit or GGUF, scroll down!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "HJbRqLynx3a8", "outputId": "05228b0d-4490-49f1-cd71-5cfe0f9417c9"}, "outputs": [{"data": {"text/plain": ["('lora_model/tokenizer_config.json',\n", " 'lora_model/special_tokens_map.json',\n", " 'lora_model/tokenizer.json')"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["model.save_pretrained(\"lora_model\")  # Local saving\n", "tokenizer.save_pretrained(\"lora_model\")\n", "# model.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving\n", "# tokenizer.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving"]}, {"cell_type": "markdown", "metadata": {"id": "mwIRb8DByBGg"}, "source": ["Now if you want to load the LoRA adapters we just saved for inference, set `False` to `True`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "UPHJs9wDyBbN", "outputId": "77165645-7402-4347-a613-f0ad90b572d9"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Setting `pad_token_id` to `eos_token_id`:128001 for open-end generation.\n"]}, {"data": {"text/plain": ["[\"<|begin_of_text|>Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\\n\\n### Instruction:\\nWhat is a famous tall tower in Paris?\\n\\n### Input:\\n\\n\\n### Response:\\nThe Eiffel Tower is a famous tall tower in Paris. It is a wrought iron tower located on the Champ de Mars in Paris, France. The tower is named after the engineer <PERSON><PERSON>, the main designer, and was built as the entrance to the 1889 World's Fair. The tower\"]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["if False:\n", "    from unsloth import FastLanguageModel\n", "    model, tokenizer = FastLanguageModel.from_pretrained(\n", "        model_name = \"lora_model\", # YOUR MODEL YOU USED FOR TRAINING\n", "        max_seq_length = max_seq_length,\n", "        dtype = dtype,\n", "        load_in_4bit = load_in_4bit,\n", "    )\n", "    FastLanguageModel.for_inference(model) # Enable native 2x faster inference\n", "\n", "# alpaca_prompt = You MUST copy from above!\n", "\n", "inputs = tokenizer(\n", "[\n", "    alpaca_prompt.format(\n", "        \"What is a famous tall tower in Paris?\", # instruction\n", "        \"\", # input\n", "        \"\", # output - leave this blank for generation!\n", "    )\n", "], return_tensors = \"pt\").to(\"cuda\")\n", "\n", "outputs = model.generate(**inputs, max_new_tokens = 64, use_cache = True)\n", "tokenizer.batch_decode(outputs)"]}, {"cell_type": "markdown", "metadata": {"id": "JPSrEDuoyE5_"}, "source": ["You can also use Hugging Face's `AutoModelForPeftCausalLM`. Only use this if you do not have `unsloth` installed. It can be hopelessly slow, since `4bit` model downloading is not supported, and Unsloth's **inference is 2x faster**."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "sTO5VnItyC08"}, "outputs": [], "source": ["if False:\n", "    # I highly do NOT suggest - use Unsloth if possible\n", "    from peft import AutoPeftModelForCausalLM\n", "    from transformers import AutoTokenizer\n", "    model = AutoPeftModelForCausalLM.from_pretrained(\n", "        \"lora_model\", # YOUR MODEL YOU USED FOR TRAINING\n", "        load_in_4bit = load_in_4bit,\n", "    )\n", "    tokenizer = AutoTokenizer.from_pretrained(\"lora_model\")"]}, {"cell_type": "markdown", "metadata": {"id": "l10uNsFYyGav"}, "source": ["### Saving to float16 for VLLM\n", "\n", "We also support saving to `float16` directly. Select `merged_16bit` for float16 or `merged_4bit` for int4. We also allow `lora` adapters as a fallback. Use `push_to_hub_merged` to upload to your Hugging Face account! You can go to https://huggingface.co/settings/tokens for your personal tokens."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "fZR6n7DsyHu6"}, "outputs": [], "source": ["# Merge to 16bit\n", "if False: model.save_pretrained_merged(\"model\", tokenizer, save_method = \"merged_16bit\",)\n", "if False: model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"merged_16bit\", token = \"\")\n", "\n", "# Merge to 4bit\n", "if False: model.save_pretrained_merged(\"model\", tokenizer, save_method = \"merged_4bit\",)\n", "if False: model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"merged_4bit\", token = \"\")\n", "\n", "# Just LoRA adapters\n", "if False: model.save_pretrained_merged(\"model\", tokenizer, save_method = \"lora\",)\n", "if False: model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"lora\", token = \"\")"]}, {"cell_type": "markdown", "metadata": {"id": "ssjW3ST2yI1L"}, "source": ["### GGUF / llama.cpp Conversion\n", "To save to `GGUF` / `llama.cpp`, we support it natively now! We clone `llama.cpp` and we default save it to `q8_0`. We allow all methods like `q4_k_m`. Use `save_pretrained_gguf` for local saving and `push_to_hub_gguf` for uploading to HF.\n", "\n", "Some supported quant methods (full list on our [Wiki page](https://github.com/unslothai/unsloth/wiki#gguf-quantization-options)):\n", "* `q8_0` - Fast conversion. High resource use, but generally acceptable.\n", "* `q4_k_m` - Recommended. Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q4_K.\n", "* `q5_k_m` - Recommended. Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q5_K."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "FKVTNAwyyKFR"}, "outputs": [], "source": ["# Save to 8bit Q8_0\n", "if False: model.save_pretrained_gguf(\"model\", tokenizer,)\n", "if False: model.push_to_hub_gguf(\"hf/model\", tokenizer, token = \"\")\n", "\n", "# Save to 16bit GGUF\n", "if False: model.save_pretrained_gguf(\"model\", tokenizer, quantization_method = \"f16\")\n", "if False: model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"f16\", token = \"\")\n", "\n", "# Save to q4_k_m GGUF\n", "if False: model.save_pretrained_gguf(\"model\", tokenizer, quantization_method = \"q4_k_m\")\n", "if False: model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"q4_k_m\", token = \"\")"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"006d2e9396414270ba092f0117b38399": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0217a84114504cc2a2d68a0d75b093ef": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "065928bbb1844d828f752d9aeec4d438": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_8cd62b09be894f6d83ebb9122052f7b0", "IPY_MODEL_9cb62d4137254277a8245f67254364a7", "IPY_MODEL_e2d233d08d354060a15322013ba67c00"], "layout": "IPY_MODEL_88373dd83bff487fbf3f933d0aa4ec82"}}, "06906e6f5a804f568382f50ba6bead84": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "07f607466207436286302004aca8b9c8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_156621bd27dc4fffa3928bca9dac7e25", "placeholder": "​", "style": "IPY_MODEL_7de44361a92d498b8aeec9876020a9be", "value": " 16000/16000 [00:00&lt;00:00, 58622.54 examples/s]"}}, "0ae9d22e24664a44b318008a1f0dbb38": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0bda54a16c44400ab266e18abb7395af": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0bf68aa188364c5e8516bd9658ef4987": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "0ea6a30a030747338d0d269a5964154e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0f33c83f72a644d197e0d22aba5354d7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "10150def49574b8c9697d69b3068df4a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ca1bae6d505c4513bddb1666478adec8", "max": 16000, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_c2ee270cf91f4eee8354b2a056d997ce", "value": 16000}}, "13a59e79b8c1400aabffe376477223b9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_2b4cb8cc837346a89e5558579f7e89eb", "IPY_MODEL_2f11689c28274c3687ae6654152a7ae6", "IPY_MODEL_3e930eaedc1b468090be01a77b3e71be"], "layout": "IPY_MODEL_d39c3584f185426fbc29458e2121a1db"}}, "14b54e5181514e908be0e74d65d8fe8a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "156621bd27dc4fffa3928bca9dac7e25": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "174616a5de704ac5a7ff310be7de4c47": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1b3a3c76a4db4f51a25cd2515bf11c69": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1bbc105c29d440beb80594727cf1c2e8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_aef0c23ef24d4f79ab5ccc6d36a65043", "placeholder": "​", "style": "IPY_MODEL_deaefdc2e5d441268cbee1775e1f712a", "value": "tokenizer.json: 100%"}}, "1fef5b3b0e3a49a98690087c371a238f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "20855c80476e4276ac89a1de038629a0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "222e6815f0904ed4a861235b0d699794": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "236c3af33e97493ead42e43b95f2e73b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7771d92cc04747abafb8689e017508f1", "max": 449, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_4c821ee18e6c45c5aedd826fd7b39209", "value": 449}}, "26b3e7f04643427996105bcce54480bc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "29303e7ab9f444f4a3b7372f17b9f9bc": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2a408a0e8df142cf8d953ba33cb3fb5e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "2b4cb8cc837346a89e5558579f7e89eb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1fef5b3b0e3a49a98690087c371a238f", "placeholder": "​", "style": "IPY_MODEL_37abe3080ffd4c2190aa387c17bde383", "value": "Downloading data: 100%"}}, "2b82ee66f1c3411ab727a09be83c3c5a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4210cdc5f8b44a60961261f45a782269", "placeholder": "​", "style": "IPY_MODEL_af7b3740d81a4cd1a5d7ce3c06239a3d", "value": " 5.70G/5.70G [00:49&lt;00:00, 95.5MB/s]"}}, "2f11689c28274c3687ae6654152a7ae6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f3cf4c7b080844d8972c62d920cd6f12", "max": 34098509, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_eb83ea8a549c414abf26567c951632f4", "value": 34098509}}, "33c7551358bf42c3a5a3e0d987377a54": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "35ee77b4c52e4d61b3a1779e2826ef93": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "37abe3080ffd4c2190aa387c17bde383": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "37d84aac6794488f9e573e5ef3ee7eb6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "3be775e3950349afac0d1677db27d36d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_6f4db5b0ce954161a5da5ad53457286c", "IPY_MODEL_4291b6a90b824776bd3115b892de4f2e", "IPY_MODEL_5976c94a9c0449c480e9e91faf039724"], "layout": "IPY_MODEL_b4461b01dcb04063a4cbdcafe7b06a71"}}, "3e930eaedc1b468090be01a77b3e71be": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_33c7551358bf42c3a5a3e0d987377a54", "placeholder": "​", "style": "IPY_MODEL_dddcec9e675f42fe895ccb315dbbf6b7", "value": " 34.1M/34.1M [00:00&lt;00:00, 85.3MB/s]"}}, "40122d0b9fc448e2bc2e793040ae8614": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "408004b178e94b4b8e279c58b7e2bd2e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4210cdc5f8b44a60961261f45a782269": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4291b6a90b824776bd3115b892de4f2e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fe131f004161495f8729bcbd4663c50c", "max": 16000, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_0bf68aa188364c5e8516bd9658ef4987", "value": 16000}}, "4b617d72867444ca9da6ab3570a3eac8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7cab13ec19f344e4b549e11e354aa72a", "IPY_MODEL_5d3d72f6e9254406a3e97aa250b86aa4", "IPY_MODEL_83c3d3b719a84fabb73ff8f965acf2e3"], "layout": "IPY_MODEL_14b54e5181514e908be0e74d65d8fe8a"}}, "4c821ee18e6c45c5aedd826fd7b39209": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "5452a280b4554784a9517e62bdd04714": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1bbc105c29d440beb80594727cf1c2e8", "IPY_MODEL_ce6c757ff6b54cb3aa717e405f4a9ecc", "IPY_MODEL_cf18e9aa457f43b4997f59d1c6db6d4f"], "layout": "IPY_MODEL_40122d0b9fc448e2bc2e793040ae8614"}}, "5683d080de874033b14d9720799c3032": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5976c94a9c0449c480e9e91faf039724": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ad28db7e853f48bdba90d2da5cf26d24", "placeholder": "​", "style": "IPY_MODEL_0bda54a16c44400ab266e18abb7395af", "value": " 16000/16000 [00:02&lt;00:00, 5906.05 examples/s]"}}, "59f41e47f54c4d74b963c76361d17b24": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5d3d72f6e9254406a3e97aa250b86aa4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_68e05c5302b3431b811b26c8d8db3903", "max": 490, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_26b3e7f04643427996105bcce54480bc", "value": 490}}, "60d0e2a6bc2c4615b9c49a995c3aa1ad": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0f33c83f72a644d197e0d22aba5354d7", "placeholder": "​", "style": "IPY_MODEL_222e6815f0904ed4a861235b0d699794", "value": "generation_config.json: 100%"}}, "62664c70849c4efaa650e70ea91f2ac3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8eeb5fbc4bef4f81a0189a938b0df88e", "placeholder": "​", "style": "IPY_MODEL_afbcd15b5e2f42b88be4bba8af63a712", "value": "special_tokens_map.json: 100%"}}, "689432885aa14636bbbbfde2ba2f039b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "68e05c5302b3431b811b26c8d8db3903": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6eb5b5bfae5140df89e32e7868ab4e98": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6f4db5b0ce954161a5da5ad53457286c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d9eceda806d44cd5b10a4aa2c822a189", "placeholder": "​", "style": "IPY_MODEL_d74bb432b30e4a96ac8719364a4fa093", "value": "Map: 100%"}}, "7090b566c853469abe05448b5394a718": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_59f41e47f54c4d74b963c76361d17b24", "max": 1140, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_37d84aac6794488f9e573e5ef3ee7eb6", "value": 1140}}, "740e9ac282de489997a6da2fd4dd280e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_689432885aa14636bbbbfde2ba2f039b", "max": 131, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_8dd3a708204d496aa458013bcd1cbe07", "value": 131}}, "74e2098b2abc4511980e7cc8389fdea3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7771d92cc04747abafb8689e017508f1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "784302c339174535a5b16eef09331d9d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "79073b3e86794ab29b99310f1186a22a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7b8d69f00f964491b78857c918723d5a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d447f6c5960b46b9b86e1b2c82ba211a", "placeholder": "​", "style": "IPY_MODEL_c96d2a6a7b364996b3882d08abaf5be6", "value": "Generating train split: 100%"}}, "7cab13ec19f344e4b549e11e354aa72a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bc4dbff595784744aa6fdf3d8293455e", "placeholder": "​", "style": "IPY_MODEL_0217a84114504cc2a2d68a0d75b093ef", "value": "Downloading readme: 100%"}}, "7cef798c923c471f87ddb35f7fa886b5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7de44361a92d498b8aeec9876020a9be": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7ef2a9f4439347b5b00cbded2f49dbcb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_91c6b2c7a75b45dd9800a5e01718da15", "placeholder": "​", "style": "IPY_MODEL_ddb30a3d95504080aa3157ba2d7a5cd7", "value": "model.safetensors: 100%"}}, "7f0eaebaebb944d99a291119dac94faf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7fbe04b431f64106a9a168deb13fdd47": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "814512fef9164353b858bd62e2f562c6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_62664c70849c4efaa650e70ea91f2ac3", "IPY_MODEL_236c3af33e97493ead42e43b95f2e73b", "IPY_MODEL_8ba5519a325a46dba5e4c973903a8729"], "layout": "IPY_MODEL_f7b370463938455ea3da9c7c5629b873"}}, "81e19ef5f8ac42cf8a0faaf3814a9fbb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0ae9d22e24664a44b318008a1f0dbb38", "placeholder": "​", "style": "IPY_MODEL_d6a2fef5b01b4cbe91632a315a0203de", "value": " 1.14k/1.14k [00:00&lt;00:00, 57.6kB/s]"}}, "83c3d3b719a84fabb73ff8f965acf2e3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_97b041295cd0499f90678a30887bd17d", "placeholder": "​", "style": "IPY_MODEL_06906e6f5a804f568382f50ba6bead84", "value": " 490/490 [00:00&lt;00:00, 33.1kB/s]"}}, "88373dd83bff487fbf3f933d0aa4ec82": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8ba5519a325a46dba5e4c973903a8729": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_174616a5de704ac5a7ff310be7de4c47", "placeholder": "​", "style": "IPY_MODEL_408004b178e94b4b8e279c58b7e2bd2e", "value": " 449/449 [00:00&lt;00:00, 29.8kB/s]"}}, "8cd62b09be894f6d83ebb9122052f7b0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_74e2098b2abc4511980e7cc8389fdea3", "placeholder": "​", "style": "IPY_MODEL_20855c80476e4276ac89a1de038629a0", "value": "tokenizer_config.json: 100%"}}, "8dd3a708204d496aa458013bcd1cbe07": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "8eeb5fbc4bef4f81a0189a938b0df88e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "91c6b2c7a75b45dd9800a5e01718da15": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "97b041295cd0499f90678a30887bd17d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9b942981c5ce4f3aa05ded98af90828b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9cb62d4137254277a8245f67254364a7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_79073b3e86794ab29b99310f1186a22a", "max": 50599, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d716494305354dce9bcd8c7c334767d1", "value": 50599}}, "a10464b4688b4107a8829f2140a4d3d9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a11ecbe5a63a495683f37eeb9c59bf31": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e01a575bbfd84ccd9c6acb628632d848", "IPY_MODEL_10150def49574b8c9697d69b3068df4a", "IPY_MODEL_a48e289710e84e1192a8a85175e517a4"], "layout": "IPY_MODEL_fe0a86aa30134213a613bab9407a10f3"}}, "a48e289710e84e1192a8a85175e517a4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7cef798c923c471f87ddb35f7fa886b5", "placeholder": "​", "style": "IPY_MODEL_f5872532493342b68457e165ca3bc255", "value": " 16000/16000 [01:40&lt;00:00, 246.30 examples/s]"}}, "ad28db7e853f48bdba90d2da5cf26d24": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "aef0c23ef24d4f79ab5ccc6d36a65043": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "af7b3740d81a4cd1a5d7ce3c06239a3d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "afbcd15b5e2f42b88be4bba8af63a712": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b255ae12c7f445409a9d4cc6c5b44c03": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b4461b01dcb04063a4cbdcafe7b06a71": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b8563a58fa644d4a9b90b93096e675df": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bc4dbff595784744aa6fdf3d8293455e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c2ee270cf91f4eee8354b2a056d997ce": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c4f21f744c9d41f9ae0f5c7126dc983d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7ef2a9f4439347b5b00cbded2f49dbcb", "IPY_MODEL_ed0863b1a49b4ed3a58e52185449a8d0", "IPY_MODEL_2b82ee66f1c3411ab727a09be83c3c5a"], "layout": "IPY_MODEL_1b3a3c76a4db4f51a25cd2515bf11c69"}}, "c95f8f778a9844debb61f5d29bafc0eb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_f307f833808141b1ab4e3f2d498402d4", "IPY_MODEL_7090b566c853469abe05448b5394a718", "IPY_MODEL_81e19ef5f8ac42cf8a0faaf3814a9fbb"], "layout": "IPY_MODEL_b255ae12c7f445409a9d4cc6c5b44c03"}}, "c96d2a6a7b364996b3882d08abaf5be6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ca1bae6d505c4513bddb1666478adec8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cc69aadfcb99497e865c9c81c0f4b48d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cd13e7bf9c3a440aafff0e7b8152acf2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "ce6c757ff6b54cb3aa717e405f4a9ecc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ea4918431f6843199553b050c1fb7f8c", "max": 9085698, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_cd13e7bf9c3a440aafff0e7b8152acf2", "value": 9085698}}, "cf18e9aa457f43b4997f59d1c6db6d4f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b8563a58fa644d4a9b90b93096e675df", "placeholder": "​", "style": "IPY_MODEL_cc69aadfcb99497e865c9c81c0f4b48d", "value": " 9.09M/9.09M [00:04&lt;00:00, 2.04MB/s]"}}, "d39c3584f185426fbc29458e2121a1db": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d447f6c5960b46b9b86e1b2c82ba211a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d6a2fef5b01b4cbe91632a315a0203de": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d716494305354dce9bcd8c7c334767d1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d74bb432b30e4a96ac8719364a4fa093": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d9eceda806d44cd5b10a4aa2c822a189": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dbdb211edb83430392608c9480fad2b5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ddb30a3d95504080aa3157ba2d7a5cd7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "dddcec9e675f42fe895ccb315dbbf6b7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "de4a4eb3a8464d59abcd85ab923d63ff": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9b942981c5ce4f3aa05ded98af90828b", "max": 16000, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_784302c339174535a5b16eef09331d9d", "value": 16000}}, "deaefdc2e5d441268cbee1775e1f712a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "df6173f6ab4b447aab5ce8a00d5e1e69": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_60d0e2a6bc2c4615b9c49a995c3aa1ad", "IPY_MODEL_740e9ac282de489997a6da2fd4dd280e", "IPY_MODEL_fbd87205bc6040069c1e1cbbabc1eac0"], "layout": "IPY_MODEL_0ea6a30a030747338d0d269a5964154e"}}, "e01a575bbfd84ccd9c6acb628632d848": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6eb5b5bfae5140df89e32e7868ab4e98", "placeholder": "​", "style": "IPY_MODEL_7f0eaebaebb944d99a291119dac94faf", "value": "Map: 100%"}}, "e2d233d08d354060a15322013ba67c00": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_006d2e9396414270ba092f0117b38399", "placeholder": "​", "style": "IPY_MODEL_dbdb211edb83430392608c9480fad2b5", "value": " 50.6k/50.6k [00:00&lt;00:00, 1.36MB/s]"}}, "ea4918431f6843199553b050c1fb7f8c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "eb83ea8a549c414abf26567c951632f4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "ed0863b1a49b4ed3a58e52185449a8d0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_29303e7ab9f444f4a3b7372f17b9f9bc", "max": 5702746405, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2a408a0e8df142cf8d953ba33cb3fb5e", "value": 5702746405}}, "f307f833808141b1ab4e3f2d498402d4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a10464b4688b4107a8829f2140a4d3d9", "placeholder": "​", "style": "IPY_MODEL_7fbe04b431f64106a9a168deb13fdd47", "value": "config.json: 100%"}}, "f3cf4c7b080844d8972c62d920cd6f12": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f43beff2d5e742e28208a19bd326ce39": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7b8d69f00f964491b78857c918723d5a", "IPY_MODEL_de4a4eb3a8464d59abcd85ab923d63ff", "IPY_MODEL_07f607466207436286302004aca8b9c8"], "layout": "IPY_MODEL_35ee77b4c52e4d61b3a1779e2826ef93"}}, "f5872532493342b68457e165ca3bc255": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f7b370463938455ea3da9c7c5629b873": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fa0feeb82a6646dcb2bd784687b7d95d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "fbd87205bc6040069c1e1cbbabc1eac0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5683d080de874033b14d9720799c3032", "placeholder": "​", "style": "IPY_MODEL_fa0feeb82a6646dcb2bd784687b7d95d", "value": " 131/131 [00:00&lt;00:00, 9.76kB/s]"}}, "fe0a86aa30134213a613bab9407a10f3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fe131f004161495f8729bcbd4663c50c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}}}}, "nbformat": 4, "nbformat_minor": 0}