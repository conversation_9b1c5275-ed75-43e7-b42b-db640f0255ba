#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并发性能测试脚本
测试不同并发配置下的翻译性能，找到最适合的并发数
"""

import json
import time
import os
import requests
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Tuple
import logging
from tqdm import tqdm
import statistics
import psutil
import subprocess

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ConcurrencyTester:
    """并发性能测试器"""

    def __init__(self, model_name: str = "/home/<USER>/Model/LLM/Qwen/Qwen3-4B-AWQ",
                 base_url: str = "http://localhost:8666"):
        self.model_name = model_name
        self.base_url = base_url
        self.api_url = f"{base_url}/v1/chat/completions"
        self.session = requests.Session()

        # 配置session
        self.session.headers.update({
            'Connection': 'keep-alive',
            'Content-Type': 'application/json'
        })

    def check_vllm_connection(self) -> bool:
        """检查VLLM服务是否可用"""
        try:
            test_payload = {
                "model": self.model_name,
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10,
                "temperature": 0.1
            }

            response = self.session.post(self.api_url, json=test_payload, timeout=30)
            response.raise_for_status()
            logger.info("✅ VLLM服务连接正常")
            return True
        except Exception as e:
            logger.error(f"❌ 无法连接到VLLM服务: {e}")
            return False

    def create_test_data(self, count: int = 20) -> List[Dict]:
        """创建测试数据"""
        test_items = []
        for i in range(count):
            item = {
                "row_number": i + 1,
                "problem": f"Calculate the limit of sequence a_n = {i+1}/n as n approaches infinity.",
                "generated_solution": f"To find the limit of a_n = {i+1}/n as n approaches infinity, we use the definition of limits. Since the numerator {i+1} is constant and the denominator n approaches infinity, the fraction approaches 0.",
                "expected_answer": "0"
            }
            test_items.append(item)
        return test_items

    def translate_single_item(self, item: Dict, max_retries: int = 2) -> Tuple[bool, float]:
        """翻译单个条目，返回(成功状态, 耗时)"""
        start_time = time.time()

        prompt = f"""请将以下JSON对象中的指定字段翻译成中文，并返回完整的JSON格式。

原始JSON：
{json.dumps(item, ensure_ascii=False, indent=2)}

翻译要求：
"problem": 需要翻译为中文（类型：problem）
"generated_solution": 需要翻译为中文（类型：solution）
"expected_answer": 需要翻译为中文（类型：answer）

翻译规则：
1. 保持所有数学公式、符号、数字完全不变
2. 直接将指定字段的内容翻译为中文，覆盖原字段内容
3. 保持其他字段不变
4. 返回完整的JSON对象，字段名保持不变，只翻译字段内容
5. 不要添加任何解释、说明、思考过程或其他文本
6. 不要使用代码块格式，直接返回纯JSON
7. 重要：字段值必须是纯文本字符串，不要在字段值中嵌套JSON对象
8. 确保返回的是有效的JSON格式

请直接返回翻译后的完整JSON对象："""

        payload = {
            "model": self.model_name,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.1,
            "max_tokens": 4096,
            "stream": False,
            "extra_body": {
                "chat_template_kwargs": {
                    "enable_thinking": False
                }
            }
        }

        for attempt in range(max_retries):
            try:
                response = self.session.post(self.api_url, json=payload, timeout=60)
                response.raise_for_status()

                result = response.json()
                translated_text = result['choices'][0]['message']['content'].strip()

                # 简单验证是否包含中文
                if any('\u4e00' <= char <= '\u9fff' for char in translated_text):
                    duration = time.time() - start_time
                    return True, duration

            except Exception as e:
                if attempt < max_retries - 1:
                    time.sleep(1)
                    continue
                else:
                    logger.warning(f"翻译失败: {e}")

        duration = time.time() - start_time
        return False, duration

    def test_concurrency_level(self, test_data: List[Dict], max_workers: int) -> Dict:
        """测试特定并发级别的性能"""
        logger.info(f"🧪 测试并发数: {max_workers}")

        start_time = time.time()
        success_count = 0
        failed_count = 0
        durations = []

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_item = {
                executor.submit(self.translate_single_item, item): item
                for item in test_data
            }

            # 收集结果
            with tqdm(total=len(test_data), desc=f"并发{max_workers}", leave=False) as pbar:
                for future in as_completed(future_to_item):
                    success, duration = future.result()
                    durations.append(duration)

                    if success:
                        success_count += 1
                    else:
                        failed_count += 1

                    pbar.update(1)

        total_time = time.time() - start_time

        # 计算统计信息
        avg_duration = statistics.mean(durations) if durations else 0
        median_duration = statistics.median(durations) if durations else 0
        throughput = len(test_data) / total_time if total_time > 0 else 0
        success_rate = success_count / len(test_data) * 100 if test_data else 0

        # 获取系统资源使用情况
        cpu_percent = psutil.cpu_percent()
        memory_info = psutil.virtual_memory()

        return {
            'max_workers': max_workers,
            'total_time': total_time,
            'success_count': success_count,
            'failed_count': failed_count,
            'success_rate': success_rate,
            'avg_duration': avg_duration,
            'median_duration': median_duration,
            'throughput': throughput,
            'durations': durations,
            'cpu_percent': cpu_percent,
            'memory_percent': memory_info.percent,
            'memory_available_gb': memory_info.available / (1024**3)
        }

    def run_comprehensive_test(self, test_count: int = 20) -> List[Dict]:
        """运行全面的并发测试"""
        if not self.check_vllm_connection():
            return []

        # 创建测试数据
        test_data = self.create_test_data(test_count)
        logger.info(f"📊 创建了 {len(test_data)} 个测试条目")

        # 测试不同的并发级别
        concurrency_levels = [1, 2, 3, 5, 8, 10, 12, 15, 20]
        results = []

        print(f"\n🚀 开始并发性能测试")
        print(f"测试条目数: {test_count}")
        print(f"测试并发级别: {concurrency_levels}")
        print("=" * 60)

        for max_workers in concurrency_levels:
            try:
                result = self.test_concurrency_level(test_data, max_workers)
                results.append(result)

                # 实时显示结果
                print(f"并发数 {max_workers:2d}: "
                      f"总时间 {result['total_time']:6.1f}s, "
                      f"吞吐量 {result['throughput']:5.2f}条/s, "
                      f"成功率 {result['success_rate']:5.1f}%, "
                      f"平均延迟 {result['avg_duration']:5.2f}s, "
                      f"CPU {result['cpu_percent']:4.1f}%, "
                      f"内存 {result['memory_percent']:4.1f}%")

                # 避免过载，测试间隔
                time.sleep(2)

            except Exception as e:
                logger.error(f"测试并发数 {max_workers} 时出错: {e}")
                continue

        return results

    def analyze_results(self, results: List[Dict]) -> Dict:
        """分析测试结果并给出建议"""
        if not results:
            return {}

        print("\n" + "=" * 60)
        print("📈 性能分析结果")
        print("=" * 60)

        # 找到最佳配置
        best_throughput = max(results, key=lambda x: x['throughput'])
        best_success_rate = max(results, key=lambda x: x['success_rate'])
        lowest_latency = min(results, key=lambda x: x['avg_duration'])

        print(f"🏆 最高吞吐量: 并发数 {best_throughput['max_workers']}, "
              f"{best_throughput['throughput']:.2f} 条/s")
        print(f"✅ 最高成功率: 并发数 {best_success_rate['max_workers']}, "
              f"{best_success_rate['success_rate']:.1f}%")
        print(f"⚡ 最低延迟: 并发数 {lowest_latency['max_workers']}, "
              f"{lowest_latency['avg_duration']:.2f}s")

        # 综合评分 (吞吐量 * 成功率 / 延迟)
        for result in results:
            if result['avg_duration'] > 0:
                result['score'] = (result['throughput'] * result['success_rate']) / result['avg_duration']
            else:
                result['score'] = 0

        best_overall = max(results, key=lambda x: x['score'])

        print(f"\n🎯 综合最佳配置: 并发数 {best_overall['max_workers']}")
        print(f"   - 吞吐量: {best_overall['throughput']:.2f} 条/s")
        print(f"   - 成功率: {best_overall['success_rate']:.1f}%")
        print(f"   - 平均延迟: {best_overall['avg_duration']:.2f}s")
        print(f"   - 综合评分: {best_overall['score']:.2f}")

        return {
            'best_throughput': best_throughput,
            'best_success_rate': best_success_rate,
            'lowest_latency': lowest_latency,
            'best_overall': best_overall,
            'all_results': results
        }

def main():
    """主函数"""
    print("🧪 VLLM并发性能测试工具")
    print("=" * 60)

    # 获取用户输入
    try:
        test_count = int(input("请输入测试条目数量 (建议10-30): ") or "20")
    except ValueError:
        test_count = 20

    # 创建测试器
    tester = ConcurrencyTester()

    # 运行测试
    results = tester.run_comprehensive_test(test_count)

    if results:
        # 分析结果
        analysis = tester.analyze_results(results)

        # 保存结果
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        result_file = f"concurrency_test_results_{timestamp}.json"

        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2)

        print(f"\n💾 测试结果已保存到: {result_file}")

        # 给出建议
        if 'best_overall' in analysis:
            best = analysis['best_overall']
            print(f"\n💡 建议配置:")
            print(f"   在 json_FY.py 中设置:")
            print(f"   max_concurrent = {best['max_workers']}  # VLLM内部并发限制")
            print(f"   max_file_workers = {min(5, best['max_workers'])}  # 文件处理并发")
    else:
        print("❌ 测试失败，请检查VLLM服务状态")

if __name__ == "__main__":
    main()
